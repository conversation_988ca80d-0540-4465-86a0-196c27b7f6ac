# encoding: utf-8

import json

from django.contrib.auth import get_user_model
from django.http.response import JsonResponse
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from .code import RetCode

USER_MODEL = get_user_model()
import traceback


class BaseView(View):
    @property
    def response(self):
        return {
            "status": False,
            "message": "",
            "code": 412
        }

    def ok(self, data=None, message="请求成功"):
        response_body = self.response
        response_body['status'] = True
        response_body['message'] = message
        response_body['code'] = 200
        if data:
            response_body['data'] = data
        return JsonResponse(response_body)

    def fail(self, code=412, message="请求失败"):
        response_body = self.response
        response_body['status'] = False
        response_body['message'] = message
        response_body['code'] = code
        return JsonResponse(data=response_body,status=code)

    def of(self, code: RetCode, message=None, data=None):
        response_body = self.response
        response_body["code"] = code.code
        response_body["message"] = message if message else code.msg
        if code is RetCode.OK:
            response_body["status"] = True
        if data:
            response_body["data"] = data
        return JsonResponse(response_body)

    @csrf_exempt
    def dispatch(self, request, *args, **kwargs):
        content_type = 'application/json'
        if request.method in ['PUT', 'DELETE', 'POST']:
            if request.body:
                try:
                    body = request.body
                    if isinstance(request.body, bytes):
                        try:
                            body = str(request.body, 'utf8')
                        except Exception:
                            body = request.body
                    if isinstance(body, str):
                        try:
                            body = json.loads(body)
                        except Exception:
                            body = request.body
                    setattr(request, request.method, body)
                except Exception:
                    print(traceback.format_exc())
                    response = self.response
                    response['status'] = False
                    response['message'] = 'request data decode error'
                    response['code'] = 401
                    return JsonResponse(response)
            else:
                setattr(request, request.method, dict())
        response = super().dispatch(request, *args, **kwargs)
        response['Access-Control-Allow-Origin'] = '*'
        return response
