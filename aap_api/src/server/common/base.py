#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
通用基础功能
"""
import logging

log = logging.getLogger("django")


def check_auth_code(request):
    """
    检查认证代码
    :param request: 请求对象
    :return: 创建者信息或None
    """
    # 简化实现 - 总是返回默认用户
    auth_code = request.META.get('HTTP_AUTHORIZATION', '')
    if auth_code:
        log.debug(f"Auth code received: {auth_code}")
        return "system"  # 返回默认用户
    else:
        log.warning("No auth code provided")
        return "anonymous"  # 匿名用户
