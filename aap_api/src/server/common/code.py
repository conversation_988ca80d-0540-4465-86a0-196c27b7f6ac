#!/usr/bin/env python
# -*- coding: UTF-8 -*-
from enum import Enum


class RetCode(Enum):
    """返回码枚举类"""

    OK = (200, "ok")
    BAD_REQUEST = (400, "bad request")
    INTERNAL_SERVER_ERROR = (500, "internal server error")

    # common（40000~40099）
    UNAUTHORIZED = (40000, "unauthorized")
    LIST_INVALID_PAGE_NUMBER = (40001, "invalid pageNumber")
    LIST_INVALID_PAGE_SIZE = (40002, "invalid pageSize")
    LIST_PAGE_SIZE_IS_TOO_LARGE = (40003, "pageSize is too large")
    INCOMPLETE_PARAMETERS = (40004, "incomplete parameters")

    # user（40100~40199）
    USER_INVALID_USERNAME = (40100, "invalid username")
    USER_INCORRECT_USERNAME_PASSWORD = (40101, "incorrect username / password")
    # study（40200~40299）
    STUDY_ORTHANC_ID_IS_EMPTY = (40200, "studyId is empty")
    STUDY_TAGS_IS_EMPTY = (40201, "tags is empty")
    STUDY_METADATA_IS_EMPTY = (40202, "metadata is empty")
    STUDY_INSTANCE_UID_IS_EMPTY = (40203, "studyInstanceUID is empty")
    STUDY_NOT_FOUND = (40204, "study not found")
    STUDY_TOSHIBA_CTP_SERIES_NO_FOUND = (40205, "toshiba ctp series not found")
    STUDY_TOSHIBA_CTP_SERIES_HAS_BEEN_PUSHED = (40206, "toshiba ctp series has been pushed")
    STUDY_INVALID_ALGORITHM_TYPE = (40207, "invalid algorithm type")
    STUDY_INVALID_AIF_INDEXES = (40208, "invalid AIF indexes")
    STUDY_INVALID_VOF_INDEXES = (40209, "invalid VOF indexes")
    STUDY_SERIES_NOT_FOUND = (40210, "series not found")
    STUDY_ALGORITHM_TASK_NOT_FOUND = (40211, "algorithm task not found")
    STUDY_ALGORITHM_TASK_PROCESSING = (40212, "algorithm task processing")
    STUDY_ALGORITHM_PUSH_ERROR = (40213, "algorithm push error")
    # series（40300~40399）
    SERIES_ORTHANC_ID_IS_EMPTY = (40300, "seriesId is empty")
    SERIES_TAGS_IS_EMPTY = (40301, "tags is empty")
    SERIES_METADATA_IS_EMPTY = (40302, "metadata is empty")
    SERIES_INSTANCE_UID_IS_EMPTY = (40303, "seriesInstanceUID is empty")
    SERIES_PACS_IMAGES_ARE_NOT_ENOUGH = (40304, "pacs images are not enough")
    SERIES_CTP_IMAGES_ARE_NOT_ENOUGH = (40305, "ctp image are not enough")
    SERIES_INVALID_SLICE_THICKNESS = (40306, "invalid slice thickness")
    SERIES_IMAGE_SLICE_THICKNESS_OUT_OF_RANGE = (40307, "single image slice thickness out of range")
    SERIES_ALL_SLICE_THICKNESS_OUT_OF_RANGE = (40308, "all image slice thickness out of range")
    SERIES_ALGORITHM_QUEUE_NOT_FOUND = (40309, "algorithm queue not found")
    SERIES_PUSH_ERROR = (40310, "series push error")
    SERIES_NOT_FOUND = (40311, "series not found")
    SERIES_ALGORITHM_TYPE_IS_EMPTY = (40312, "algorithmType is empty")
    SERIES_INVALID_ALGORITHM_TYPE = (40313, "invalid algorithmType")
    SERIES_ALGORITHM_RESULT_NOT_FOUND = (40314, "algorithm result not found")
    SERIES_INVALID_ASPECTS_SCORE = (40315, "invalid score")
    SERIES_CPR_NOT_FOUND = (40316, "cpr not found")
    SERIES_TASK_NOT_FOUND = (40317, "task not found")
    SERIES_TDC_NOT_FOUND = (40318, "TDC curve not found")
    # image（40400~40400）
    IMAGE_ORTHANC_ID_IS_EMPTY = (40400, "instanceId is empty")
    IMAGE_TAGS_IS_EMPTY = (40401, "tags is empty")
    IMAGE_METADATA_IS_EMPTY = (40402, "metadata is empty")
    IMAGE_SOP_INSTANCE_UID_IS_EMPTY = (40403, "sopInstanceUID is empty")
    IMAGE_ALREADY_EXISTS = (40404, "image already exists")
    # ge api
    GE_APPLY_CACL_SCHEDULE_ERROR = (40501, "GE applyCalcSchedule error")
    # config
    CONFIG_CODE_IS_EMPTY = (40600, "code is empty")
    CONFIG_INVALID_CODE = (40601, "invalid code")
    CONFIG_DUPLICATE_CODE = (40602, "duplicate code")
    CONFIG_CODE_IS_NOT_CUSTOM = (40603, "code is not custom")


    @property
    def code(self):
        """
        获取状态码
        """
        return self.value[0]

    @property
    def msg(self):
        """
        获取状态码信息
        """
        return self.value[1]
    
class Const:
    DEFAULT_HOSPITAL_UID = "0"
    MODALITY_CT = "CT"
    OTHER_MODALITIES = ["MR"]
    ALGORITHM_TYPE_OTHER = "other"
    ALGORITHM_RESULT_MANUFACTURER = "UnionStrong"