#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
序列相关模型
"""
from django.db import models
from server.study.models import Study


class Series(models.Model):
    """序列模型"""
    uuid = models.CharField(max_length=100, unique=True, verbose_name="UUID")
    hospital_uid = models.CharField(max_length=100, verbose_name="医院UID")
    study = models.ForeignKey(Study, on_delete=models.CASCADE, verbose_name="检查")
    series_instance_uid = models.CharField(max_length=255, unique=True, verbose_name="序列实例UID")
    orthanc_id = models.CharField(max_length=100, blank=True, verbose_name="Orthanc ID")
    series_number = models.CharField(max_length=20, blank=True, verbose_name="序列号")
    series_description = models.CharField(max_length=500, blank=True, verbose_name="序列描述")
    modality = models.Char<PERSON><PERSON>(max_length=20, blank=True, verbose_name="设备类型")
    body_part_examined = models.CharField(max_length=100, blank=True, verbose_name="检查部位")
    slice_thickness = models.CharField(max_length=50, blank=True, verbose_name="层厚")
    spacing_between_slices = models.CharField(max_length=50, blank=True, verbose_name="层间距")
    rows = models.IntegerField(default=0, verbose_name="行数")
    columns = models.IntegerField(default=0, verbose_name="列数")
    pixel_spacing = models.CharField(max_length=100, blank=True, verbose_name="像素间距")
    type = models.CharField(max_length=50, blank=True, verbose_name="算法类型")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        db_table = 'series'
        verbose_name = "序列"
        verbose_name_plural = "序列"
        indexes = [
            models.Index(fields=['hospital_uid']),
            models.Index(fields=['series_instance_uid']),
            models.Index(fields=['study']),
        ]

    def __str__(self):
        return f"{self.series_description} - {self.series_instance_uid}"


class SeriesAlgorithm(models.Model):
    """序列算法模型"""
    uuid = models.CharField(max_length=100, unique=True, verbose_name="UUID")
    series = models.ForeignKey(Series, on_delete=models.CASCADE, verbose_name="序列")
    algorithm_type = models.CharField(max_length=50, verbose_name="算法类型")
    status = models.CharField(max_length=20, default="pending", verbose_name="状态")
    result = models.TextField(blank=True, verbose_name="结果")
    error_message = models.TextField(blank=True, verbose_name="错误信息")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        db_table = 'series_algorithm'
        verbose_name = "序列算法"
        verbose_name_plural = "序列算法"
        indexes = [
            models.Index(fields=['series']),
            models.Index(fields=['algorithm_type']),
            models.Index(fields=['status']),
        ]

    def __str__(self):
        return f"{self.algorithm_type} - {self.status}"


class AlgorithmTask(models.Model):
    """算法任务模型"""
    uuid = models.CharField(max_length=100, unique=True, verbose_name="UUID")
    series_uid = models.CharField(max_length=255, verbose_name="序列UID")
    algorithm_type = models.CharField(max_length=50, verbose_name="算法类型")
    user_id = models.CharField(max_length=100, verbose_name="用户ID")
    status = models.CharField(max_length=20, default="pending", verbose_name="状态")
    result = models.TextField(blank=True, verbose_name="结果")
    error_message = models.TextField(blank=True, verbose_name="错误信息")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        db_table = 'algorithm_task'
        verbose_name = "算法任务"
        verbose_name_plural = "算法任务"
        indexes = [
            models.Index(fields=['series_uid']),
            models.Index(fields=['algorithm_type']),
            models.Index(fields=['status']),
        ]

    def __str__(self):
        return f"{self.algorithm_type} - {self.series_uid}"
