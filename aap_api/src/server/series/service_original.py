#!/usr/bin/env python
# -*- coding: UTF-8 -*-

import logging
import os
import os.path
import threading
import time
import traceback
import uuid
import shutil
import numpy as np
# import SimpleITK as sitk  # 暂时注释掉，需要时再安装
from pandas.io.json import json_normalize
from PIL import Image
import SimpleIT<PERSON> as sitk
from django import db

from server import settings
from server.image.models import CallBackDICOM
from server.common.code import RetCode, Const, Config
from server.series.models import Series, AlgorithmTask
from server.systemconfig.system_config_utils import SystemConfigUtils
from server.ge.ge_api import validate_dicom_failed
from server.common.remote_api import RabbitMQProducer, OrthancApi


log = logging.getLogger("django")

DCM_DIR = os.path.join(settings.DOCKER_DATA_BASE_DIR, "dcm")


class SeriesCallbackService:

    def __init__(self, series_orthanc_id, tags, hospital_uid, hospital_name):
        self.series_orthanc_id = series_orthanc_id
        self.tags = tags
        self.hospital_uid = hospital_uid
        self.hospital_name = hospital_name
        self.study_instance_uid = None
        self.series_instance_uid = self.tags.get("SeriesInstanceUID")
        self.resource_uid = F"{self.study_instance_uid},{self.series_instance_uid}"
        ge_callback = int(SystemConfigUtils.get_config(code=Config.GE_CALLBACK_SWITCH, def_value="0"))
        self.ge_callback = True if ge_callback else False

    def do_post(self):
        """
        序列回调请求体处理入口
        :return:
        """
        series = Series.objects.filter(hospital_uid=self.hospital_uid,
                                       series_instance_uid=self.series_instance_uid).first()
        if not series:
            log.error("Series[callback] > series[{}] not found".format(self.series_instance_uid))
            return RetCode.SERIES_NOT_FOUND
        self.study_instance_uid = series.study.study_instance_uid
        threading.Thread(target=self.__async,
                         args=(series.id, self.series_orthanc_id, self.ge_callback)).start()
        return RetCode.OK

    @staticmethod
    def __async(series_id, orthanc_id, ge_callback):
        """
        异步处理入口
        :param series_id: 序列ID
        :param orthanc_id: 序列Orthanc标识
        :param ge_callback: GE接入标识
        :return:
        """
        db.close_old_connections()
        handler = SeriesPushHandler(series_id, orthanc_id, ge_callback)
        handler.start()


class SeriesPushHandler:

    def __init__(self, series_id, series_orthanc_id, ge_callback):
        series = Series.objects.get(id=series_id)
        study = series.study
        self.hospital_uid = series.hospital_uid
        self.study_instance_uid = study.study_instance_uid
        self.toshiba = study.toshiba
        self.original_series = series
        self.original_series_orthanc_id = series_orthanc_id
        self.ge_callback = ge_callback

    def start(self):
        """
        序列推送入口
        :return:
        """
        algorithm_type = self.original_series.type
        original_series_instance_uid = self.original_series.series_instance_uid
        if algorithm_type == Const.ALGORITHM_TYPE_OTHER:
            thumbnail = SeriesThumbnail.generate_thumbnail(self.study_instance_uid, original_series_instance_uid, True)
            self.original_series.thumbnail = thumbnail
            self.original_series.downloaded = True
            self.original_series.save()
            return
 
        downloader = SeriesDownloader(self.study_instance_uid, original_series_instance_uid,
                                      self.original_series_orthanc_id, False)
        if not downloader.run():
            log.info("Series[callback] > study:{}, series:{}, failed to download dicom".format(
                self.study_instance_uid, original_series_instance_uid))
            return
        if is_toshiba_ctp:
            log.info("Series[callback] > study:{}, series:{}, toshiba ctp series, ignore push".format(
                self.study_instance_uid, self.original_series_orthanc_id))
            thumbnail_path = SeriesThumbnail.generate_thumbnail(self.study_instance_uid, original_series_instance_uid)
            self.original_series.thumbnail_path = thumbnail_path
            self.original_series.downloaded = True
            self.original_series.save()
            return

        series_split = [self.original_series]
        splitter = SeriesSplitter(original_series_instance_uid)
        new_series = splitter.split()
        log.info("Series[callback] > study:{}, series:{}, split {} series".format(
            self.study_instance_uid, original_series_instance_uid, len(new_series)))
        series_split.extend(new_series)
        for series in series_split:
            thumbnail_path = SeriesThumbnail.generate_thumbnail(self.study_instance_uid, series.series_instance_uid)
            series.thumbnail_path = thumbnail_path
            series.downloaded = True
            series.save()

    def call_ge(self, series_instance_uid, is_split_series):
        """
        向魔盒申请调度资源
        :param series_instance_uid: 序列标识
        :param is_split_series: 是否为拆分序列
        :return:
        """
        calc_no = f"{self.study_instance_uid},{series_instance_uid}"
        resource_type = "series"
        resource_id = series_instance_uid
        if is_split_series:
            resource_type = "series_split"
            resource_id = self.original_series.series_instance_uid
        ret_code = ge_apply_calc_schedule_notice(calc_no, resource_type, resource_id)
        log.info("GE[apply] > series:{}, calcNo:{}, resourceId:{}, resourceType:{}, result:{}".format(
            series_instance_uid, calc_no, resource_id, resource_type, ret_code))
        return

    @staticmethod
    def calculate_series(series, resource_data=None):
        """
        计算序列
        :param series: 序列信息
        :param resource_data: GE授予计算相关信息
        {"resourceId": "GPU ID  不大于7；[0-7]","resourceType": "GPU 类型","resourceCapacity": "GPU 容量",
        "resourceNode": "GPU所在位置","resourceMetadata": "GPU 元数据"}）
        :return:
        """
        study_instance_uid = series.study.study_instance_uid
        series_instance_uid = series.series_instance_uid
        algorithm_type = series.type
        # 创建算法任务
        algorithm_task = AlgorithmTask.objects.create(uuid=str(uuid.uuid1()), series_uid=series_instance_uid,
                                                      algorithm_type=algorithm_type, user_id="admin")
        queue_name = settings.TYPE_QUEUE_DICT.get(algorithm_type, None)
        message = dict(studyInstanceUID=study_instance_uid, seriesInstanceUID=series_instance_uid,
                       algorithmType=algorithm_type, resourceData=resource_data if resource_data else dict())
        # 是否回传
        callback = SystemConfigUtils().getConfigValue(code="canReportBack", def_value="")
        message["callback"] = eval(callback) if callback else True
        # 串并行
        message["taskType"] = SystemConfigUtils().getConfigValue(code="algorithmProcessMode", def_value="1")
        try:
            RabbitMQProducer.simple_send(queue_name=queue_name, message=message)
            return RetCode.OK
        except Exception:
            log.error(f"Series[callback] send message error: {traceback.format_exc()}")
            algorithm_task.delete()
            if resource_data:
                # 同步GE计算结果
                storage_data(calcNo=F"{study_instance_uid},{series_instance_uid}", aiModel=algorithm_type,
                             aiResultMsg={RetCode.SERIES_PUSH_ERROR.code, RetCode.SERIES_PUSH_ERROR.msg})
            return RetCode.SERIES_PUSH_ERROR


class SeriesThumbnail:
    @staticmethod
    def generate_thumbnail(study_instance_uid, series_instance_uid, use_orthanc=False):
        """
        生成序列缩略图
        :param study_instance_uid: 检查标识
        :param series_instance_uid: 序列标识
        :param use_orthanc: 使用orthanc显示缩略图
        :return:
        """
        callback_dicom_queryset = CallBackDICOM.objects.filter(study_instance_uid=study_instance_uid,
                                                               series_instance_uid=series_instance_uid)
        image_count = callback_dicom_queryset.count()
        thumbnail_index = int(image_count / 2)
        if thumbnail_index == 0:
            thumbnail_index = 1
        callback_dicom_list = callback_dicom_queryset.all().order_by("instance_number")[
                              thumbnail_index - 1:thumbnail_index]
        if not callback_dicom_list.exists():
            return ""
        thumbnail_dicom = callback_dicom_list[0]
        log.info("Thumbnail[study:{}, series:{}] > image count:{}, thumbnail index:{}, instance number:{}".format(
            study_instance_uid, series_instance_uid, image_count, thumbnail_index, thumbnail_dicom.instance_number))
        if use_orthanc:
            return thumbnail_dicom.sop_orthanc_uuid
        thumbnail_dir = os.path.join(settings.DOCKER_DATA_BASE_DIR, "static", study_instance_uid, "THUMBNAIL")
        if not os.path.exists(thumbnail_dir):
            os.makedirs(thumbnail_dir)
        thumbnail_path = os.path.join(thumbnail_dir, "{}.png".format(thumbnail_index))
        if os.path.exists(thumbnail_path):
            os.remove(thumbnail_path)
        try:
            itk_img = sitk.ReadImage(os.path.join(settings.DOCKER_DATA_BASE_DIR, thumbnail_dicom.path[1:]))
            itk_img = sitk.IntensityWindowing(itk_img, 0, 100, 0, 255)
            img_array = sitk.GetArrayFromImage(itk_img)
            shape = img_array.shape
            img_array = np.reshape(img_array, (shape[1], shape[2]))
            high_window = np.max(img_array)
            low_window = np.min(img_array)
            lung_win = np.array([low_window * 1., high_window * 1.])
            new_img = (img_array - lung_win[0]) / (lung_win[1] - lung_win[0])
            scaled_image = np.uint8(new_img * 255)
            final_image = Image.fromarray(scaled_image)
            final_image.save(thumbnail_path)
            return thumbnail_path.replace(settings.DOCKER_DATA_BASE_DIR, "")
        except:
            log.error("Thumbnail[study:{}, series:{}] > gerenate error:{}".format(
                study_instance_uid, series_instance_uid, traceback.format_exc()))
            return ""


class SeriesDownloader:
    def __init__(self, study_instance_uid, series_instance_uid, series_orthanc_id, is_toshiba_ctp):
        self.study_instance_uid = study_instance_uid
        self.series_instance_uid = series_instance_uid
        self.series_orthanc_id = series_orthanc_id
        self.target_dir = os.path.join(DCM_DIR, study_instance_uid if is_toshiba_ctp else series_instance_uid)
        self.fail_counter = 0

    def run(self):
        """
        下载原图
        :return:
        """
        start = time.time()
        image_list = OrthancApi.query_image(self.series_orthanc_id)
        query_size = len(image_list)
        if settings.DUPLICATED_INSTANCES_FIX:
            data_frame = json_normalize(image_list)
            duplicated_instances = data_frame.duplicated(subset=["ParentSeries", "MainDicomTags.InstanceNumber"])
            deduplicated_list, deleted_list = [], []
            for index, value in duplicated_instances.items():
                if value:
                    deleted_list.append(data_frame.iloc[[index]][["MainDicomTags.SOPInstanceUID"]].values[0][0])
                    continue
                deduplicated_list.append(image_list[index])
            if deleted_list:
                CallBackDICOM.objects.filter(sop_instance_uid__in=deleted_list).delete()
                log.info("DICOM[study:{},series:{}] > delete {} images".format(
                    self.study_instance_uid, self.series_instance_uid, len(deleted_list)))
            image_list = deduplicated_list
        for image in image_list:
            file_uid = image.get("FileUuid")
            sop_instance_uid = image.get("MainDicomTags", {}).get("SOPInstanceUID")
            self.copy_dicom(file_uid, sop_instance_uid)
        log.info("DICOM[study:{},series:{}] > download {}/{} dicom in {:.2f}s".format(
            self.study_instance_uid, self.series_instance_uid, len(image_list), query_size, (time.time() - start)))
        return self.fail_counter == 0

    def copy_dicom(self, file_uid, sop_instance_uid):
        """
        拷贝原图
        :param file_uid: 文件标识
        :param sop_instance_uid: 实例标识
        :return:
        """
        if len(file_uid) <= 4:
            return
        file_path = os.path.join(settings.ORTHANC_STORAGE_PATH, file_uid[0:2], file_uid[2:4], file_uid)
        try:
            if not os.path.exists(self.target_dir):
                os.makedirs(self.target_dir, exist_ok=True)
            target_path = os.path.join(self.target_dir, "{}.dcm".format(sop_instance_uid))
            # 避免重复下载
            old_file = os.path.join(self.target_dir, file_uid)
            if os.path.exists(old_file):
                os.rename(old_file, target_path)
            if not os.path.exists(target_path):
                shutil.copyfile(file_path, target_path)
            path = target_path.replace(settings.DOCKER_DATA_BASE_DIR, "")
            CallBackDICOM.objects.filter(sop_instance_uid=sop_instance_uid).update(path=path)
        except:
            self.fail_counter += 1
            log.error("DCM[study:{},series:{}] > failed to download {}, {}".format(
                self.study_instance_uid, self.series_instance_uid, file_path, traceback.format_exc()))


