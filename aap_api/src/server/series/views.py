#!/usr/bin/env python
# -*- coding: UTF-8 -*-
import logging
# Create your views here.
from server.common.code import RetCode, Const
from server.common.views import BaseView
from server.series.service import SeriesCallbackService

log = logging.getLogger("django")


class SeriesCallbackView(BaseView):
    """序列回调重构"""

    def post(self, request):
        """
        序列回调

        :param request:
        :return:
        """
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        request_body = request.POST
        # 参数校验
        hospital_uid = request_body.get("hospitalUID", "0")
        hospital_name = request_body.get("hospitalName", "")
        series_orthanc_id = request_body.get("seriesId", None)
        tags = request_body.get("tags", None)
        if not series_orthanc_id:
            return self.of(RetCode.SERIES_ORTHANC_ID_IS_EMPTY)
        if not tags:
            return self.of(RetCode.SERIES_TAGS_IS_EMPTY)
        series_instance_uid = tags.get("SeriesInstanceUID", "")
        if not series_instance_uid:
            return self.of(RetCode.SERIES_INSTANCE_UID_IS_EMPTY)
        if tags.get("Manufacturer", "") == Const.ALGORITHM_RESULT_MANUFACTURER:
            log.info("Series[callback] > series:{}, ignore result callback".format(series_instance_uid))
            return self.ok(message="result callback")
        log.info("Series[{}] > start callback".format(series_instance_uid))
        callback_service = SeriesCallbackService(series_orthanc_id, tags, hospital_uid, hospital_name)
        ret_code = callback_service.do_post()
        log.info("Series[callback] > response: {}".format(ret_code.msg))
        return self.of(ret_code)
