#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
简化版序列服务 - 用于基本功能测试
"""

import logging
import os
import threading
import time
import traceback
import uuid
from django import db

from server import settings
from server.image.models import CallBackDICOM
from server.common.code import RetCode, Const, Config
from server.series.models import Series, AlgorithmTask
from server.systemconfig.system_config_utils import SystemConfigUtils
from server.ge.ge_api import validate_dicom_failed
from server.common.remote_api import RabbitMQProducer, OrthancApi

log = logging.getLogger("django")

DCM_DIR = os.path.join(settings.DOCKER_DATA_BASE_DIR, "dcm")


class SeriesCallbackService:
    """序列回调服务 - 简化版"""
    
    def __init__(self, series_instance_uid, series_orthanc_id, tags, hospital_uid, hospital_name):
        self.series_instance_uid = series_instance_uid
        self.series_orthanc_id = series_orthanc_id
        self.tags = tags
        self.hospital_uid = hospital_uid
        self.hospital_name = hospital_name
        self.study_instance_uid = None
        self.resource_uid = f"{self.study_instance_uid},{self.series_instance_uid}"
        
        try:
            ge_callback_str = SystemConfigUtils.get_config(code=Config.GE_CALLBACK_SWITCH, def_value="0")
            ge_callback = int(ge_callback_str) if ge_callback_str else 0
            self.ge_callback = True if ge_callback else False
        except (ValueError, TypeError):
            self.ge_callback = False

    def do_post(self):
        """
        序列回调请求体处理入口
        :return:
        """
        series = Series.objects.filter(hospital_uid=self.hospital_uid,
                                       series_instance_uid=self.series_instance_uid).first()
        if not series:
            log.error("Series[callback] > series[{}] not found".format(self.series_instance_uid))
            return RetCode.SERIES_NOT_FOUND
        
        self.study_instance_uid = series.study.study_instance_uid
        threading.Thread(target=self.__async,
                         args=(series.id, self.series_orthanc_id, self.ge_callback)).start()
        return RetCode.OK

    @staticmethod
    def __async(series_id, orthanc_id, ge_callback):
        """
        异步处理入口
        :param series_id: 序列ID
        :param orthanc_id: 序列Orthanc标识
        :param ge_callback: GE接入标识
        :return:
        """
        db.close_old_connections()
        handler = SeriesPushHandler(series_id, orthanc_id, ge_callback)
        handler.start()


class SeriesPushHandler:
    """序列推送处理器 - 简化版"""

    def __init__(self, series_id, series_orthanc_id, ge_callback):
        series = Series.objects.get(id=series_id)
        study = series.study
        self.hospital_uid = series.hospital_uid
        self.study_instance_uid = study.study_instance_uid
        self.original_series = series
        self.original_series_orthanc_id = series_orthanc_id
        self.ge_callback = ge_callback

    def start(self):
        """
        开始处理
        :return:
        """
        log.info("SeriesPushHandler started for series: {}".format(self.original_series.series_instance_uid))
        
        # 简化处理逻辑
        algorithm_type = self.original_series.type or Const.ALGORITHM_TYPE_OTHER
        original_series_instance_uid = self.original_series.series_instance_uid
        
        if algorithm_type == Const.ALGORITHM_TYPE_OTHER:
            log.info("Series[callback] > series type is OTHER, skipping processing")
            return

        # 下载原图
        downloader = SeriesDownloader(self.study_instance_uid, original_series_instance_uid, 
                                      self.original_series_orthanc_id, False)
        downloader.run()
        
        log.info("Series[callback] > processing completed for series: {}".format(original_series_instance_uid))


class SeriesDownloader:
    """序列下载器 - 简化版"""
    
    def __init__(self, study_instance_uid, series_instance_uid, series_orthanc_id, is_toshiba_ctp):
        self.study_instance_uid = study_instance_uid
        self.series_instance_uid = series_instance_uid
        self.series_orthanc_id = series_orthanc_id
        self.target_dir = os.path.join(DCM_DIR, study_instance_uid if is_toshiba_ctp else series_instance_uid)
        self.fail_counter = 0

    def run(self):
        """
        下载原图 - 简化版
        :return:
        """
        start = time.time()
        try:
            image_list = OrthancApi.query_image(self.series_orthanc_id)
            query_size = len(image_list)
            
            log.info("DICOM[study:{},series:{}] > start downloading {} images".format(
                self.study_instance_uid, self.series_instance_uid, query_size))
            
            # 简化下载逻辑 - 只记录，不实际下载
            for image in image_list:
                sop_instance_uid = image.get("MainDicomTags", {}).get("SOPInstanceUID", "")
                if sop_instance_uid:
                    log.debug("Would download image: {}".format(sop_instance_uid))
            
            end = time.time()
            log.info("DICOM[study:{},series:{}] > download completed in {:.2f}s".format(
                self.study_instance_uid, self.series_instance_uid, end - start))
                
        except Exception as e:
            log.error("Error downloading series {}: {}".format(self.series_instance_uid, e))
            log.error(traceback.format_exc())


class SeriesThumbnail:
    """序列缩略图生成器 - 简化版"""
    
    @staticmethod
    def generate_thumbnail(study_instance_uid, series_instance_uid, is_toshiba_ctp):
        """
        生成缩略图 - 简化版
        :param study_instance_uid: 检查实例UID
        :param series_instance_uid: 序列实例UID
        :param is_toshiba_ctp: 是否为东芝CTP
        :return: 缩略图路径
        """
        log.info("Generating thumbnail for series: {}".format(series_instance_uid))
        
        # 简化实现 - 返回占位符路径
        thumbnail_path = f"/thumbnails/{series_instance_uid}.jpg"
        log.info("Thumbnail generated: {}".format(thumbnail_path))
        
        return thumbnail_path
