import os

_env = os.environ
DEBUG = True
DB_HOST = _env.get('DB_HOST', '************')
DB_USER = _env.get('DB_USER', 'root')
DB_PASSWORD = _env.get('DB_PASSWORD', 'UnionStrong@2020')
DB_PORT = _env.get('DB_PORT', "3310")


DOCKER_DATA_BASE_DIR = _env.get("DOCKER_DATA_BASE_DIR", "/code/data")

# pacs 服务器
ORTHANC_HOST = _env.get("ORTHANC_HOST", "************")
ORTHANC_WEB_PORT = int(_env.get("ORTHANC_WEB_PORT", 8044))
ORTHANC_WADO_USERNAME = _env.get("ORTHANC_WADO_USERNAME", "unionstrong")
ORTHANC_WADO_PASSWORD = _env.get("ORTHANC_WADO_PASSWORD", "UnionStrong@2020")

# Pacs本地服务器：
LOCAL_AET = _env.get("LOCAL_AET", "UNIONSTRONG")

MQ_HOST = _env.get("MQ_HOST", "************")
MQ_PORT = _env.get("MQ_PORT", 5673)
MQ_USERNAME = _env.get("MQ_USERNAME", "unionstrong")
MQ_PASSWORD = _env.get("MQ_PASSWORD", "UnionStrong@2020")

ORTHANC_STORAGE_PATH = _env.get("ORTHANC_STORAGE_PATH", "/clouddata/pacs/db-v6")