#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
图像相关模型
"""
from django.db import models


class CallBackDICOM(models.Model):
    """DICOM回调模型"""
    uuid = models.CharField(max_length=100, unique=True, verbose_name="UUID")
    hospital_uid = models.CharField(max_length=100, verbose_name="医院UID")
    sop_instance_uid = models.CharField(max_length=255, verbose_name="SOP实例UID")
    study_instance_uid = models.CharField(max_length=255, verbose_name="检查实例UID")
    series_instance_uid = models.CharField(max_length=255, verbose_name="序列实例UID")
    instance_number = models.IntegerField(default=0, verbose_name="实例编号")
    slice_thickness = models.CharField(max_length=50, blank=True, verbose_name="层厚")
    slice_position = models.Char<PERSON>ield(max_length=50, blank=True, verbose_name="层位置")
    sop_orthanc_uuid = models.CharField(max_length=100, verbose_name="SOP Orthanc UUID")
    protocol_name = models.CharField(max_length=255, blank=True, verbose_name="协议名称")
    path = models.CharField(max_length=500, blank=True, verbose_name="文件路径")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        db_table = 'callback_dicom'
        verbose_name = "DICOM回调"
        verbose_name_plural = "DICOM回调"
        indexes = [
            models.Index(fields=['hospital_uid', 'sop_instance_uid']),
            models.Index(fields=['study_instance_uid']),
            models.Index(fields=['series_instance_uid']),
        ]

    def __str__(self):
        return f"{self.sop_instance_uid}"
