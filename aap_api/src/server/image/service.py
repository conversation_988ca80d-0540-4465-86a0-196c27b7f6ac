#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : service
@Project : upixel-station-backend
<AUTHOR> mingxing
@Date    : 2022/8/18 10:10
"""
import datetime
import json
import logging
import traceback
import uuid

from server.async.models import ConfigSet
from server.image.models import CallBackDICOM
from server.common.code import RetCode, Const, Config
from server.common.remote_api import RabbitMQProducer
from server.series.models import Series, SeriesAlgorithm
from server.study.models import Study, Hospital
from server.systemconfig.system_config_utils import SystemConfigUtils

log = logging.getLogger("django")


class ImageCallbackService:
    def __init__(self, image_orthanc_id, tags, hospital_uid, hospital_name):
        self.image_orthanc_id = image_orthanc_id
        self.tags = tags
        self.hospital_uid = hospital_uid
        self.hospital_name = hospital_name

    def do_post(self):
        """
        保存标签

        :return:
        """
        self.__save_hospital()
        study, _is_created = self.__save_study()
        series = self.__save_series(study)
        self.__save_image(series)
        if _is_created:
            # 新增检查通知
            notify_message = {"action": "new", "data": {"StudyInstanceUID": study.study_instance_uid}}
            RabbitMQProducer.ws_notify("ws.notify.study_create", notify_message)
        return RetCode.OK

    @staticmethod
    def match_algorithm(tags):
        """
        匹配算法

        :param tags: 标签列表
        :return:
        """
        study_instance_uid = tags.get("StudyInstanceUID")
        series_instance_uid = tags.get("SeriesInstanceUID")
        sop_instance_uid = tags.get("SOPInstanceUID")
        configset_queryset = ConfigSet.objects.filter(is_delete=False).values("algorithm_type", "key", "value")
        if not configset_queryset.exists():
            log.info("study:{}, series:{}, algorithm keywords not found".format(
                study_instance_uid, series_instance_uid))
            return "", []

        for configset in configset_queryset:
            algorithm_type = configset.get("algorithm_type", "")
            tag_name = configset.get("key", "")
            keywords = configset.get("value", "").split(";")
            keywords = [keyword for keyword in keywords if keyword and keyword.strip()]
            if not algorithm_type or not tag_name or not keywords:
                log.debug("invalid configset:{}".format(configset))
                continue
            tag_value = tags.get(tag_name, "")
            log.info("study:{}, series:{}, image:{}, {}:{}, algorithm：{}, keywords:{}".format(
                study_instance_uid, series_instance_uid, sop_instance_uid,
                tag_name, tag_value, algorithm_type, keywords))
            if any([True for text in keywords if tag_value.strip().upper().find(text.strip().upper()) != -1]):
                log.info("study:{}, series:{}, hit algorithm:{}".format(
                    study_instance_uid, series_instance_uid, algorithm_type))
                return algorithm_type, [tag_name, keywords]
        log.info("study:{}, series:{}, miss algorithm".format(study_instance_uid, series_instance_uid))
        return "", []

    def __save_hospital(self):
        """
        保存医院

        :return:
        """
        # 判断医院是否存在
        hospital_qs = Hospital.objects.filter(hospital_uid=self.hospital_uid)
        if hospital_qs.exists():
            return
        Hospital.objects.create(id=str(uuid.uuid1()), hospital_uid=self.hospital_uid, hospital_name=self.hospital_name)
        log.info("Image[callback] > hospital created, uid:{}, name:{}".format(self.hospital_uid, self.hospital_name))

    def __save_study(self):
        """
        保存检查

        :return:
        """
        tags = self.tags
        study_instance_uid = tags.get("StudyInstanceUID")
        # 判断检查是否存在
        study_queryset = Study.objects.filter(hospital_uid=self.hospital_uid, study_instance_uid=study_instance_uid)
        if study_queryset.exists():
            return study_queryset.first(), False
        # 新增检查
        study_datetime = None
        study_date = tags.get("StudyDate", "")
        study_time = tags.get("StudyTime", "")
        if study_date and study_time:
            dot_index = study_time.find(".")
            if dot_index > -1:
                study_time = study_time[:dot_index]
            datetime_str = F"{study_date} {study_time}"
            log.info("Image[callback] > study[{}] datetime:{}".format(study_instance_uid, datetime_str))
            try:
                study_datetime = datetime.datetime.strptime(datetime_str, '%Y%m%d %H%M%S')
            except Exception:
                log.error(traceback.format_exc())
        toshiba = SystemConfigUtils.get_config(Config.TOSHIBA_SWITCH, False)
        study = Study.objects.create(id=str(uuid.uuid1()),
                                     hospital_uid=self.hospital_uid,
                                     study_instance_uid=study_instance_uid,
                                     study_id=tags.get("StudyID", ""),
                                     study_datetime=study_datetime,
                                     study_description=tags.get("StudyDescription", ""),
                                     patient_id=tags.get("PatientID", ""),
                                     patient_name=tags.get("PatientName", ""),
                                     patient_sex=tags.get("PatientSex", ""),
                                     patient_age=tags.get("PatientAge", ""),
                                     patient_weight=tags.get("PatientWeight", ""),
                                     patient_birthdate=tags.get("PatientBirthDate", ""),
                                     api_version=Const.API_VERSION,
                                     toshiba=toshiba)
        log.info("Image[callback] > Study created, study:{}, toshiba:{}".format(study_instance_uid, toshiba))
        return study, True

    def __save_series(self, study):
        """
        保存序列

        :param study: 检查信息
        :return:
        """
        tags = self.tags
        image_orthanc_id = self.image_orthanc_id
        study_instance_uid = study.study_instance_uid
        series_instance_uid = tags.get("SeriesInstanceUID")
        # 判断序列是否存在
        series_queryset = Series.objects.filter(hospital_uid=self.hospital_uid, series_instance_uid=series_instance_uid)
        if series_queryset.exists():
            return series_queryset.first()
        # 获取算法匹配结果（调整之前是每个图像都要匹配一次算法。按序列级别匹配算法类型，提高性能）
        modality = tags.get("Modality", "").upper()
        manufacturer = tags.get("Manufacturer", "")
        series_description = tags.get("SeriesDescription", "")
        log.debug("Image[callback] > study[{}], series[{}], modality:{}, manufacturer:{}, series description:{}".format(
            study_instance_uid, series_instance_uid, modality, manufacturer, series_description))
        save_data = dict(
            id=str(uuid.uuid1()),
            hospital_uid=self.hospital_uid,
            series_instance_uid=series_instance_uid,
            modality=modality,
            series_description=series_description,
            series_number=tags.get("SeriesNumber", ""),
            series_date=tags.get("SeriesDate", ""),
            series_time=tags.get("SeriesTime", ""),
            body_part_examined=tags.get("BodyPartExamined", ""),
            slice_thickness=tags.get("SliceThickness", ""),
            thumbnail=image_orthanc_id,
            study=study
        )
        if modality == Const.MODALITY_CT and manufacturer != Const.ALGORITHM_RESULT_MANUFACTURER:
            # 只有原图，才可以进行算法匹配（算法结果序列忽略算法匹配）
            algorithm_type, comment = ImageCallbackService.match_algorithm(tags)
            if algorithm_type:
                save_data["type"] = algorithm_type
            # 记录序列算法匹配记录
            SeriesAlgorithm.objects.create(id=str(uuid.uuid1()), series_instance_uid=series_instance_uid,
                                           comment=json.dumps(comment))
        if modality in Const.OTHER_MODALITIES:
            save_data["type"] = Const.ALGORITHM_TYPE_OTHER
        # 新增序列
        series = Series.objects.create(**save_data)
        log.info("Image[callback] > Series created, study:{}, series:{}".format(study_instance_uid, series_instance_uid))
        return series

    def __save_image(self, series):
        """
        保存图像信息

        :param series: 序列信息
        :return:
        """
        tags = self.tags
        image_orthanc_id = self.image_orthanc_id
        study_instance_uid = tags.get("StudyInstanceUID")
        series_instance_uid = series.series_instance_uid
        sop_instance_uid = tags.get("SOPInstanceUID")
        # 图像是否存在
        dicom_queryset = CallBackDICOM.objects.filter(hospital_uid=self.hospital_uid, sop_instance_uid=sop_instance_uid)
        if dicom_queryset.exists():
            log.debug("Image[callback] > study[{}], series[{}], image[{}] already exists".format(
                study_instance_uid, series_instance_uid, sop_instance_uid))
            return None
        instance_number = 0
        try:
            instance_number = int(tags.get("InstanceNumber", "0"))
        except Exception:
            log.error("failed to convert InstanceNumber[{}]: {}".format(
                tags.get("InstanceNumber"), traceback.format_exc()))
        slice_position = ""
        try:
            image_position = tags.get("ImagePositionPatient", r"0\0\0")
            slice_position = image_position.split("\\")[-1]
        except:
            log.error("failed to convert ImagePosition[{}]: {}".format(
                tags.get("ImagePositionPatient"), traceback.format_exc()))
        # 新增图像
        CallBackDICOM.objects.create(uuid=str(uuid.uuid1()),
                                     hospital_uid=self.hospital_uid,
                                     sop_instance_uid=sop_instance_uid,
                                     study_instance_uid=study_instance_uid,
                                     series_instance_uid=series_instance_uid,
                                     instance_number=instance_number,
                                     slice_thickness=tags.get("SliceThickness", ""),
                                     slice_position=slice_position,
                                     sop_orthanc_uuid=image_orthanc_id,
                                     protocol_name=tags.get("ProtocolName", ""))
        log.debug("Image[callback] > Image created, study:{}, series:{}, image:{}".format(
            study_instance_uid, series_instance_uid, sop_instance_uid))

