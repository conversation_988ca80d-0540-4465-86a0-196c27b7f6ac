#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
检查相关模型
"""
from django.db import models


class Hospital(models.Model):
    """医院模型"""
    uid = models.Char<PERSON>ield(max_length=100, unique=True, verbose_name="医院UID")
    name = models.CharField(max_length=255, verbose_name="医院名称")
    address = models.CharField(max_length=500, blank=True, verbose_name="地址")
    contact = models.CharField(max_length=100, blank=True, verbose_name="联系方式")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        db_table = 'hospital'
        verbose_name = "医院"
        verbose_name_plural = "医院"

    def __str__(self):
        return self.name


class Study(models.Model):
    """检查模型"""
    uuid = models.Cha<PERSON><PERSON><PERSON>(max_length=100, unique=True, verbose_name="UUID")
    hospital_uid = models.CharField(max_length=100, verbose_name="医院UID")
    study_instance_uid = models.CharField(max_length=255, unique=True, verbose_name="检查实例UID")
    orthanc_id = models.CharField(max_length=100, blank=True, verbose_name="Orthanc ID")
    patient_id = models.CharField(max_length=100, blank=True, verbose_name="患者ID")
    patient_name = models.CharField(max_length=255, blank=True, verbose_name="患者姓名")
    patient_sex = models.CharField(max_length=10, blank=True, verbose_name="患者性别")
    patient_age = models.CharField(max_length=20, blank=True, verbose_name="患者年龄")
    patient_birth_date = models.CharField(max_length=20, blank=True, verbose_name="患者出生日期")
    study_date = models.CharField(max_length=20, blank=True, verbose_name="检查日期")
    study_time = models.CharField(max_length=20, blank=True, verbose_name="检查时间")
    study_description = models.CharField(max_length=500, blank=True, verbose_name="检查描述")
    modality = models.CharField(max_length=20, blank=True, verbose_name="设备类型")
    institution_name = models.CharField(max_length=255, blank=True, verbose_name="机构名称")
    toshiba = models.BooleanField(default=False, verbose_name="是否为东芝设备")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        db_table = 'study'
        verbose_name = "检查"
        verbose_name_plural = "检查"
        indexes = [
            models.Index(fields=['hospital_uid']),
            models.Index(fields=['study_instance_uid']),
            models.Index(fields=['patient_id']),
        ]

    def __str__(self):
        return f"{self.patient_name} - {self.study_instance_uid}"
