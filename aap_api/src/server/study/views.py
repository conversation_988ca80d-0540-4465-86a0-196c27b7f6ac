#!/usr/bin/env python
# -*- coding: UTF-8 -*-
import logging
import datetime

from django.db.models import F
# Create your views here.

from server.common.views import BaseView
from server.common.code import RetCode
from server.study.service import StudyCallbackService


log = logging.getLogger("django")

class StudyCallbackView(BaseView):
    """检查回调重构"""

    def post(self, request):
        """
        检查回调

        :param request:
        :return:
        """

        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        request_body = request.POST
        # 参数校验
        hospital_uid = request_body.get("hospitalUID", "0")
        hospital_name = request_body.get("hospitalName", "")
        study_orthanc_id = request_body.get("studyId", None)
        tags = request_body.get("tags", None)
        if not study_orthanc_id:
            return self.of(RetCode.STUDY_ORTHANC_ID_IS_EMPTY)
        if not tags:
            return self.of(RetCode.STUDY_TAGS_IS_EMPTY)
        study_instance_uid = tags.get("StudyInstanceUID", "")
        if not study_instance_uid:
            return self.of(RetCode.STUDY_INSTANCE_UID_IS_EMPTY)
        log.info("Study[{}] > start callback".format(study_instance_uid))
        callback_service = StudyCallbackService(study_instance_uid, study_orthanc_id, hospital_uid, hospital_name)
        ret_code = callback_service.do_post()
        log.info("Study[callback] > response: {}".format(ret_code.msg))
        return self.of(ret_code)
