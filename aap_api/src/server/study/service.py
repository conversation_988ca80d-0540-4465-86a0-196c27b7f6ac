#!/usr/bin/env python
# -*- coding: UTF-8 -*-
import logging
import threading

from django import db
from django.db.models import Q

from server.common.code import (
    RetCode,
    Const, Config,
)
from server.series.models import Series
from server.study.models import Study
from server.systemconfig.system_config_utils import SystemConfigUtils

log = logging.getLogger("django")


class StudyCallbackService:

    def __init__(self, study_instance_uid, study_orthanc_id, hospital_uid, hospital_name):
        self.study_instance_uid = study_instance_uid
        self.study_orthanc_id = study_orthanc_id
        self.hospital_uid = hospital_uid
        self.hospital_name = hospital_name
        ge_callback = int(SystemConfigUtils.get_config(code=Config.GE_CALLBACK_SWITCH, def_value="0"))
        self.ge_callback = True if ge_callback else False

    def do_post(self):
        """
        检查回调请求处理入口
        :return:
        """
        study_qs = Study.objects.filter(hospital_uid=self.hospital_uid, study_instance_uid=self.study_instance_uid)
        if not study_qs.exists():
            log.error("Study[callback] > study[{}] not found".format(self.study_instance_uid))
            return RetCode.STUDY_NOT_FOUND
        study_qs.update(orthanc_id=self.study_orthanc_id)
        study = study_qs.first()
        ctp_series_qs = Series.objects.filter(study__id=study.id, type=Const.ALGORITHM_TYPE_CTP)
        if ctp_series_qs.exists():
            threading.Thread(target=self.__async,
                             args=(study.id, self.ge_callback)).start()
        return RetCode.OK

    @staticmethod
    def __async(study_id, ge_callback):
        db.close_old_connections()
        handler = StudyPushHandler(study_id, ge_callback)
        handler.start()
