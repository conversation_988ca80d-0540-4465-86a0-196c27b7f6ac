#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
系统配置工具类
"""
import logging

log = logging.getLogger("django")


class SystemConfigUtils:
    """系统配置工具类"""

    @staticmethod
    def get_config(code, def_value=None):
        """
        获取配置值
        :param code: 配置代码
        :param def_value: 默认值
        :return: 配置值
        """
        try:
            from server.config_models.models import ConfigSet
            config = ConfigSet.objects.filter(code=code).first()
            if config:
                return config.value
            else:
                log.warning("Config not found for code: %s, using default value: %s", code, def_value)
                return def_value
        except Exception as e:
            log.error("Error getting config for code %s: %s", code, e)
            return def_value

    @staticmethod
    def set_config(code, value, description=""):
        """
        设置配置值
        :param code: 配置代码
        :param value: 配置值
        :param description: 描述
        :return: 是否成功
        """
        try:
            from server.config_models.models import ConfigSet
            config, created = ConfigSet.objects.get_or_create(
                code=code,
                defaults={'value': value, 'description': description}
            )
            if not created:
                config.value = value
                config.description = description
                config.save()
            return True
        except Exception as e:
            log.error("Error setting config for code %s: %s", code, e)
            return False
