#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
异步任务相关模型
"""
from django.db import models


class ConfigSet(models.Model):
    """配置集合模型"""
    code = models.CharField(max_length=100, unique=True, verbose_name="配置代码")
    value = models.TextField(verbose_name="配置值")
    description = models.CharField(max_length=255, blank=True, verbose_name="描述")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        db_table = 'config_set'
        verbose_name = "配置集合"
        verbose_name_plural = "配置集合"

    def __str__(self):
        return f"{self.code}: {self.value}"
