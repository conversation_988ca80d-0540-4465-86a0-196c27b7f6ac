#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
异步任务相关模型
"""
from django.db import models

MAX_LENGTH = 255


class ConfigSet(models.Model):
    """
    ctp 和aspect 动态设置触发推送条件
    """
    uuid = models.CharField(primary_key=True, verbose_name='uuid', max_length=MAX_LENGTH)
    algorithm_type = models.CharField(verbose_name='算法类型',
                                      max_length=MAX_LENGTH,
                                      null=True,
                                      blank=True
                                      )
    key = models.TextField(verbose_name="key值",
                           null=True,
                           blank=True)
    value = models.TextField(verbose_name="value值",
                             null=True,
                             blank=True)
    timestamp = models.DateTimeField(verbose_name='创建时间',
                                     auto_now_add=True)
    comment = models.TextField('备注', null=True, blank=True)
    is_delete = models.BooleanField(default=False, verbose_name='是否更新，如果更新设置为删除状态',
                                    blank=True)

    class Meta:
        db_table = 'push_config'
        verbose_name = '推送配置表'
        verbose_name_plural = '推送配置表'

    def to_dict(self):
        return dict(
            uuid=self.uuid,
            algorithm_type=self.algorithm_type,
            key=self.key,
            value=self.value,
            comment=self.comment,
            timestamp=self.timestamp.strftime("%Y-%m-%d")
        )

