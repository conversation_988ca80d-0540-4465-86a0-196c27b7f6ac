"""
Django settings for server project.

Generated by 'django-admin startproject' using Django 2.0.5.

For more information on this file, see
https://docs.djangoproject.com/en/2.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/2.0/ref/settings/
"""

import os
import pymysql


pymysql.install_as_MySQLdb()

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/2.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = '^=anqo92^jf+xb3nq&yy&v!aibw-afjnh(gii25k1(e&wxyq%_'

# SECURITY WARNING: don't run with d2ebug turned on in production!
DEBUG = False

ALLOWED_HOSTS = ['*']

# Application definition

INSTALLED_APPS = [
    # 'concurrent_log_handler',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'server.api',
    'server.study',
    'server.series',
    'server.image',
    'server.config_models',
]

MIDDLEWARE = [
    'server.common.middleware.DisableCSRFCheck',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'server.urls'
TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, "server/static/report/templates")],
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'server.wsgi.application'

# 添加配置文件变量
from server.config import *

# Internationalization
# https://docs.djangoproject.com/en/2.0/topics/i18n/

LANGUAGE_CODE = 'zh-hans'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_L10N = True

USE_TZ = False

PER_PAGE_COUNT = 10

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/2.0/howto/static-files/


STATIC_URL = 'server/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'server/static')
STATIC_ROOT_URL = os.path.join(BASE_DIR, 'server/static')


LOG_DIR = os.path.join(os.environ.get("DOCKER_DATA_BASE_DIR", "/code/data"), 'log/aap-api')
LOG_PATH = os.path.join(LOG_DIR, 'aap-api.log')
if not os.path.exists(LOG_DIR):
    try:
        os.makedirs(LOG_DIR, exist_ok=True)
    except PermissionError:
        pass




_env = os.environ
LOCAL_WEB_SERVER_HOST = _env.get("LOCAL_WEB_SERVER_HOST", "webapi")
LOG_DEBUG = _env.get("LOG_DEBUG", "1")
LOG_FILE_SIZE = _env.get("LOG_FILE_SIZE")

try:
    if LOG_FILE_SIZE and isinstance(LOG_FILE_SIZE, str):
        LOG_FILE_SIZE = int(LOG_FILE_SIZE)
    else:
        LOG_FILE_SIZE = 30
except:
    LOG_FILE_SIZE = 30

LOGGING = {
    'version': 1,
    'disable_existing_loggers': True,  # 是否禁用已经存在的日志器
    'filters': {
    },

    'formatters': {
        'standard': {
            'format': f"%(asctime)s[{LOCAL_WEB_SERVER_HOST}][%(levelname)s][%(process)d-%(thread)d][%(filename)s-%(lineno)s]: %(message)s"
        }
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'concurrent_log_handler.ConcurrentRotatingFileHandler',
            'filename': LOG_PATH,
            'filters': ['skip_unreadable_posts'],
            'formatter': 'standard',
            'maxBytes': 1024 * 1024 * LOG_FILE_SIZE,
            'backupCount': 3
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'filters': ['skip_unreadable_posts'],
            'formatter': 'standard'
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file', 'console'],
            'propagate': True,
            'level': 'DEBUG' if LOG_DEBUG == "1" else "INFO",
        }
    }
}

# Database
# https://docs.djangoproject.com/en/2.0/ref/settings/#databases
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'cloud',
        'HOST': DB_HOST,
        'USER': DB_USER,
        'PASSWORD': DB_PASSWORD,
        'PORT': DB_PORT,
        'CHARSET': 'utf8mb4',
        'COLLATION': 'utf8mb4_general_ci'
    },
}

API_VERSION = "1.0.0"
DUPLICATED_INSTANCES_FIX = int(_env.get("DUPLICATED_INSTANCES_FIX", "0"))

# 算法类型队列映射
TYPE_QUEUE_DICT = {
    "ctp": "ctp_queue",
    "other": "other_queue",
}
