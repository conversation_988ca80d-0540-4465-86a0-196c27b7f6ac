#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
GE API相关功能
"""
import logging

log = logging.getLogger("django")


def validate_dicom_failed(series_instance_uid):
    """
    验证DICOM失败处理
    :param series_instance_uid: 序列实例UID
    :return: 验证结果
    """
    log.info(f"Validating DICOM for series: {series_instance_uid}")
    # 这里应该实现具体的DICOM验证逻辑
    # 目前返回False表示验证通过
    return False


def ge_apply_calc_schedule_notice(calc_no, resource_type, resource_id):
    """
    GE申请计算调度通知
    :param calc_no: 计算编号
    :param resource_type: 资源类型
    :param resource_id: 资源ID
    :return: 返回码
    """
    log.info(f"GE apply calc schedule notice: calc_no={calc_no}, resource_type={resource_type}, resource_id={resource_id}")
    # 这里应该实现具体的GE API调用逻辑
    # 目前返回0表示成功
    return 0
