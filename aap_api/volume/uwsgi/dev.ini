# uwsig使用配置文件启动
[uwsgi]
# 项目目录
chdir=/code
# 指定项目的application
env = DJANGO_SETTINGS_MODULE=server.settings
module=server.wsgi:application
# 根据服务器核数设置
workers=4
# 设置每个工作进程的线程数
threads=8

pidfile=/code/server/uwsgi.pid
# 指定IP端口
http=aap-api:4201
# 指定静态文件
static-map=/server/static=/code/server/static
# 启动uwsgi的用户名和用户组
uid=root
gid=root
# 启用主进程
master=true
# 自动移除unix Socket和pid文件当服务停止的时候
vacuum=true
# 序列化接受的内容，如果可能的话
thunder-lock=true
# 启用线程
enable-threads=true
# 设置自中断时间
harakiri=30
# 设置缓冲
post-buffering=8192
# 设置日志目录
#disable-logging=false
#daemonize=/code/server/logs/uwsgi.log
#wsgi-file =/code/server/wsgi.py
# uwsgi缓存
cache2 = name=mycache,items=100,blocksize=5000000