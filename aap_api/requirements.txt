# 急腹症平台API核心依赖
# 清理日期: 2025-07-10
# 原始文件已备份为 requirements.txt.backup

# Django框架及相关
Django==2.0.5
gunicorn==19.9.0

# 数据库连接
PyMySQL==0.9.2

# HTTP请求和网络通信
requests==2.19.1
urllib3==1.23

# 消息队列 (RabbitMQ)
pika==0.12.0

# 医学影像处理
pydicom==1.2.2
pynetdicom==1.2.0

# 图像处理
Pillow==5.4.1
numpy==1.19.5

# 日志处理
concurrent-log-handler==0.9.20

# 日期时间处理
python-dateutil==2.8.0
pytz==2018.9

# 兼容性库
six==1.12.0

# 加密和安全
cryptography==2.3.1
certifi==2018.4.16

# 依赖库的依赖
cffi==1.15.0
pycparser==2.21
chardet==3.0.4
idna==2.7