#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : setup
@Project : ugs-api
<AUTHOR> mingxing
@Date    : 2023/3/27 13:26
"""
from setuptools import setup, find_packages, Extension
from setuptools.command.build_py import build_py as build_py_orig
from Cython.Build import cythonize


ext_modules = [
]


class BuildPy(build_py_orig):
    def build_packages(self):
        pass


setup(
    name="aap_api",
    author="unionstrong",
    url="http://************/research_projects/acute_abdomen/aap_api.git",
    cmdclass={"build_py": BuildPy},
    ext_modules=cythonize(ext_modules, language_level="3"),
    version="1.0.0",
    packages=["server"],
    package_dir={"": "src"},
    package_data={"server": ["static/img/*.png", "static/tls/*.pem"]},
    platforms="linux_x86_64",
    description="AAP API",
    long_description="AAP API",
    license="MIT"
)
