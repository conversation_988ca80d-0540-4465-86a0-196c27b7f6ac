# This file is dual licensed under the terms of the Apache License, Version
# 2.0, and the BSD License. See the LICENSE file in the root of this repository
# for complete details.

from __future__ import absolute_import, division, print_function

__all__ = [
    "__title__", "__summary__", "__uri__", "__version__", "__author__",
    "__email__", "__license__", "__copyright__",
]

__title__ = "cryptography"
__summary__ = ("cryptography is a package which provides cryptographic recipes"
               " and primitives to Python developers.")
__uri__ = "https://github.com/pyca/cryptography"

__version__ = "2.3.1"

__author__ = "The cryptography developers"
__email__ = "<EMAIL>"

__license__ = "BSD or Apache License, Version 2.0"
__copyright__ = "Copyright 2013-2017 {0}".format(__author__)
