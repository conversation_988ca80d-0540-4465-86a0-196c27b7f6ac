{"classifiers": ["Development Status :: 5 - Production/Stable", "Intended Audience :: <PERSON><PERSON><PERSON>", "Natural Language :: English", "License :: OSI Approved :: Apache Software License", "Programming Language :: Python", "Programming Language :: Python :: 2", "Programming Language :: Python :: 2.7", "Programming Language :: Python :: 3", "Programming Language :: Python :: 3.4", "Programming Language :: Python :: 3.5", "Programming Language :: Python :: 3.6", "Programming Language :: Python :: Implementation :: CPython", "Programming Language :: Python :: Implementation :: PyPy"], "description_content_type": "text/x-rst", "extensions": {"python.details": {"contacts": [{"email": "<EMAIL>", "name": "<PERSON>", "role": "author"}], "document_names": {"description": "DESCRIPTION.rst", "license": "LICENSE.txt"}, "project_urls": {"Home": "http://python-requests.org"}}}, "extras": ["security", "socks"], "generator": "bdist_wheel (0.30.0)", "license": "Apache 2.0", "metadata_version": "2.0", "name": "requests", "requires_python": ">=2.6, !=3.0.*, !=3.1.*, !=3.2.*, !=3.3.*", "run_requires": [{"extra": "socks", "requires": ["PySocks (!=1.5.7,>=1.5.6)"]}, {"requires": ["certifi (>=2017.4.17)", "chardet (<3.1.0,>=3.0.2)", "idna (<2.8,>=2.5)", "urllib3 (<1.24,>=1.21.1)"]}, {"extra": "security", "requires": ["cryptography (>=1.3.4)", "idna (>=2.0.0)", "pyOpenSSL (>=0.14)"]}, {"environment": "sys_platform == \"win32\" and (python_version == \"2.7\" or python_version == \"2.6\")", "extra": "socks", "requires": ["win-inet-pton"]}], "summary": "Python HTTP for Humans.", "test_requires": [{"requires": ["PySocks (!=1.5.7,>=1.5.6)", "pytest (>=2.8.0)", "pytest-cov", "pytest-httpbin (==0.0.7)", "pytest-mock", "pytest-xdist"]}], "version": "2.19.1"}