requests-2.19.1.dist-info/DESCRIPTION.rst,sha256=n_VLfuLN3E966vObL5yznSnEemZi9-mN_NYciEbtu1U,50439
requests-2.19.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
requests-2.19.1.dist-info/LICENSE.txt,sha256=gqhp_k6WdEmVbSalRq3HYqys4CiFLOgboWw8Wx12sVs,581
requests-2.19.1.dist-info/METADATA,sha256=xyt8lFsqw5Ub6FgGyQi_zaONQ6uCh8ULkonZeblqHzg,52012
requests-2.19.1.dist-info/RECORD,,
requests-2.19.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
requests-2.19.1.dist-info/WHEEL,sha256=kdsN-5OJAZIiHN-iO4Rhl82KyS0bDWf4uBwMbkNafr8,110
requests-2.19.1.dist-info/metadata.json,sha256=aoLNiq-pX6UqPnwJr037D19kMMoHnl8qHmS1HFmI-zw,1768
requests-2.19.1.dist-info/top_level.txt,sha256=fMSVmHfb5rbGOo6xv-O_tUX6j-WyixssE-SnwcDRxNQ,9
requests/__init__.py,sha256=CfBVrS2i2DRqW4l_YWg0amv2aE40uzKipJOhl5f_ym4,4056
requests/__pycache__/__init__.cpython-38.pyc,,
requests/__pycache__/__version__.cpython-38.pyc,,
requests/__pycache__/_internal_utils.cpython-38.pyc,,
requests/__pycache__/adapters.cpython-38.pyc,,
requests/__pycache__/api.cpython-38.pyc,,
requests/__pycache__/auth.cpython-38.pyc,,
requests/__pycache__/certs.cpython-38.pyc,,
requests/__pycache__/compat.cpython-38.pyc,,
requests/__pycache__/cookies.cpython-38.pyc,,
requests/__pycache__/exceptions.cpython-38.pyc,,
requests/__pycache__/help.cpython-38.pyc,,
requests/__pycache__/hooks.cpython-38.pyc,,
requests/__pycache__/models.cpython-38.pyc,,
requests/__pycache__/packages.cpython-38.pyc,,
requests/__pycache__/sessions.cpython-38.pyc,,
requests/__pycache__/status_codes.cpython-38.pyc,,
requests/__pycache__/structures.cpython-38.pyc,,
requests/__pycache__/utils.cpython-38.pyc,,
requests/__version__.py,sha256=rJ2xgNOLhjspGkNPfgXTBctqqvsf2uJMFTaE0rlVtbI,436
requests/_internal_utils.py,sha256=Zx3PnEUccyfsB-ie11nZVAW8qClJy0gx1qNME7rgT18,1096
requests/adapters.py,sha256=i3hSRejv346malcOuw46ivOBDxCyip0SBuMy3sylvmA,21236
requests/api.py,sha256=zub9ENcEUT2m9gwgBgqH5RIRDfrx2kwRpZ7L6hX3mcw,6261
requests/auth.py,sha256=oRSQkBYcLkTEssudkzoR1UW1Glb1ts3p1RWusgHf1YU,10208
requests/certs.py,sha256=dOB5rV2DZ13dEhq9BUa_4hd5kAqg59e_zUZB00faYz8,453
requests/compat.py,sha256=aY4WEIpX7kYXzD4h4RhcO5QwkurS79Ssg5m0fWEflMc,1723
requests/cookies.py,sha256=olUaLeNci_z1K-Bn5PeEKllSspmQqN9-s8Ug7CasaPE,18346
requests/exceptions.py,sha256=Q8YeWWxiHHXhkEynLpMgC_6_r_ZTYw2aITs9wCSAZNY,3185
requests/help.py,sha256=vKOUvN6TMonwqumMjRaG9BgsYw7-Ei6aMWY71vtHCx8,3606
requests/hooks.py,sha256=HXAHoC1FNTFRZX6-lNdvPM7Tst4kvGwYTN-AOKRxoRU,767
requests/models.py,sha256=Eus9H6VuYZ3hkvFqpK6chs5HsU4Uhubm2k7015AeRII,34095
requests/packages.py,sha256=Q2rF0L5mc3wQAvc6q_lAVtPTDOaOeFgD-7kWSQLkjEQ,542
requests/sessions.py,sha256=71MK2HCadovka1vAx9dyDFWAuw69KgRPRBpd0HWSEAo,27829
requests/status_codes.py,sha256=pgw-xlnxO5zHQWn3fKps2cxwQehKzPxEbdhIrMQe6Ec,4128
requests/structures.py,sha256=zoP8qly2Jak5e89HwpqjN1z2diztI-_gaqts1raJJBc,2981
requests/utils.py,sha256=3OxbbLUQFVdm84fdBD9nduXvhw6hIzj59mhvBomKuJI,30156
