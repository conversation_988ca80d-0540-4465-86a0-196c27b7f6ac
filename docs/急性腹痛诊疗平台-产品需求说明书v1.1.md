# 急性腹痛诊疗平台产品需求说明书

**版本：** v1.1
**日期：** 2025年7月7日
**编写人：** 产品团队
**审核人：** 技术团队

---

## 1. 项目概述

### 1.1 项目背景

急性腹痛是临床常见的急症之一，准确快速的诊断对患者预后至关重要。传统的诊疗流程依赖医生的经验判断，存在诊断效率低、标准化程度不高等问题。本项目旨在构建一个集成AI智能分析、影像标记、手术规划等功能的综合性诊疗平台，提升急性腹痛的诊疗效率和准确性。

### 1.2 项目目标

- **提升诊疗效率**：通过AI辅助诊断，减少医生诊断时间，提高工作效率
- **标准化诊疗流程**：建立标准化的诊疗流程和评估体系
- **智能化辅助决策**：提供AI驱动的智能分析和建议
- **可视化手术规划**：提供直观的手术规划工具，提高手术精确度
- **数据驱动优化**：通过数据积累不断优化诊疗算法和流程

### 1.3 项目范围

**包含范围：**
- 用户认证和权限管理系统
- DICOM数据接入和PACS系统集成
- 病例管理和影像查看系统
- AI智能对话和评估系统
- 影像标记和分析系统
- 手术规划工具
- 报告生成和导出功能

**不包含范围：**
- 移动端应用（后期扩展）
- 多语言支持（后期扩展）
- 高级PACS功能（如DICOM打印、工作列表等）

### 1.4 相关方

- **主要用户**：急诊科医生、放射科医生、外科医生
- **次要用户**：医院管理员、IT运维人员
- **开发团队**：产品经理、前端开发、后端开发、AI算法工程师
- **合作方**：Dify AI平台、TotalSegmentator算法提供方

---

## 2. 功能需求

### 2.1 用户认证系统

#### 2.1.1 登录功能
- **功能描述**：用户通过用户名和密码登录系统
- **输入**：用户名、密码
- **输出**：登录成功跳转到病例列表页，失败显示错误信息
- **业务规则**：
  - 支持记住登录状态

#### 2.1.2 权限管理
- **角色定义**：
  - 医生：查看病例、使用AI功能、标记影像、手术规划
  - 管理员：用户管理、系统配置

### 2.2 DICOM数据接入系统

#### 2.2.1 数据接入流程
- **功能描述**：通过Orthanc PACS系统接收和处理DICOM影像数据
- **技术架构**：
  - **Orthanc服务**：作为DICOM接收和存储服务器
  - **Lua回调脚本**：实现DICOM数据的自动回调处理
  - **系统集成**：通过HTTP回调接口与诊疗平台集成

#### 2.2.2 DICOM上传方式
- **Web界面上传**：
  - 通过Orthanc Web界面直接上传DICOM文件
  - 支持单文件和批量文件上传
  - 实时显示上传进度和状态
- **DICOM网络传输**：
  - 支持标准DICOM C-Store协议
  - 兼容各种医疗设备和PACS系统
  - 自动接收来自CT、MR等设备的影像数据

#### 2.2.3 自动回调机制
- **触发条件**：
  - **OnStoredInstance**：每个DICOM实例存储完成时触发
  - **OnStableSeries**：序列数据稳定后触发（默认60秒无新数据）
  - **OnStableStudy**：检查数据稳定后触发
- **过滤规则**：
  - 仅处理CT和MR模态的影像数据
  - 忽略制造商为"UNIONSTRONG"的算法结果数据
  - 自动过滤非医学影像数据
- **回调接口**：
  - `/api/v1/images/callback/`：处理单个影像实例
  - `/api/v1/series/callback/`：处理序列级别数据
  - `/api/v1/studies/callback/`：处理检查级别数据

#### 2.2.4 数据处理流程
1. **DICOM接收**：Orthanc接收并验证DICOM文件格式
2. **元数据提取**：自动提取患者信息、检查信息、序列信息
3. **回调触发**：Lua脚本检测到新数据后触发相应回调
4. **系统集成**：回调接口将数据同步到诊疗平台数据库
5. **状态更新**：更新病例状态，通知前端界面刷新

#### 2.2.5 环境配置
- **回调地址配置**：
  - 环境变量：`Lua_Script_CallBack_IP=ugs-api`
  - 环境变量：`Lua_Script_CallBack_PORT=4201`
  - 默认地址：`http://***********:8000`（备用配置）
- **网络要求**：
  - Orthanc容器与API服务容器网络互通
  - 支持HTTP POST请求传输
  - 确保回调接口的可访问性

### 2.3 病例管理系统

#### 2.3.1 病例列表
- **功能描述**：展示所有病例的概览信息
- **显示字段**：患者姓名、年龄、性别、检查日期、检查类型、状态
- **数据来源**：通过DICOM回调接口自动同步的病例数据
- **操作功能**：
  - 查看详情：点击"查看"按钮进入病例详情页
  - 搜索筛选：按患者姓名、日期范围筛选
  - 分页显示：每页显示20条记录
  - 状态标识：显示数据接收状态（接收中/完成/异常）

#### 2.3.2 病例详情
- **功能描述**：展示单个病例的详细信息和影像数据
- **显示内容**：
  - 患者基本信息：姓名、年龄、性别、病史（来自DICOM标签）
  - 检查信息：检查日期、设备信息、扫描参数
  - 影像数据：DICOM影像查看器（通过Orthanc WADO服务）
  - 影像描述信息区域
  - 操作按钮区域

### 2.4 AI智能对话系统

#### 2.4.1 对话功能
- **功能描述**：医生可与AI大模型进行医疗相关对话咨询
- **技术实现**：集成Dify平台提供的对话接口
- **功能特性**：
  - 实时对话：支持多轮对话交互
  - 聊天记录：保存完整的对话历史
  - 上下文理解：AI能理解对话上下文
- **触发方式**：点击右下角AI对话按钮弹出对话框

#### 2.4.2 对话记录管理
- **存储策略**：本地存储用户的对话历史
- **查看方式**：在对话框中可查看历史对话
- **数据保护**：对话内容仅用户本人可见==（对接已有dify工作流1，看dify是否支持新建对话，检查绑定对话ID）==

### 2.5 AI智能评估系统

#### 2.5.1 智能分析表单
- **功能描述**：基于患者信息进行AI智能分析
- **表单字段**：
  - 自动填充：姓名、年龄、性别（从DICOM数据自动获取）
  - 手动填入：症状描述、病史、体征等
- **提交处理**：表单提交后调用AI分析接口==（对接已有dify工作流2）==

#### 2.5.2 评估结果管理
- **覆盖策略**：新的评估结果覆盖之前的结果
- **数据持久化**：保存最新的评估内容
- **编辑功能**：支持在上次内容基础上继续编辑

#### 2.5.3 报告生成
- **报告格式**：PDF格式的诊疗报告
- **生成逻辑**：基于AI评估结果自动生成
- **下载功能**：点击报告按钮可下载PDF文件
- **状态提示**：首次访问时提示"没有报告"

### 2.6 影像标记系统

#### 2.6.1 手动标记功能
- **界面切换**：点击标记按钮，标记工具组件替换影像描述信息区域。**==增加返回按钮。点击返回，回到AI评估页面。==**
- **标记流程**：
  1. 添加标记：点击"添加标记"按钮创建新标记标签
  2. 标签管理：支持重命名、删除、修改颜色
  3. 画笔工具：可调节画笔粗细和形状
  4. 图像标记：在影像上进行标记操作
  5. 数据保存：保存所有帧的标记数据**==（前端VTK.js实现3D Model）==**
- **3D显示**：点击3D按钮进入3D显示模式

#### 2.6.2 自动标记功能
- **算法集成**：集成TotalSegmentator自动分割算法
- **处理流程**：
  1. 触发：点击自动标记按钮
  2. 进度显示：展示算法计算进度条
  3. 结果回显：算法完成后显示标记结果
- **兼容性**：自动标记和手动标记可同时存在

### 2.7 手术规划系统

#### 2.7.1 规划工具
- **界面切换**：点击手术规划显示规划工具区域，隐藏标记相关区域
- **工具功能**：

  - 靶点选择：使用十字线工具选择手术靶点
  - 入点选择：使用十字线工具选择手术入点
  - 针道显示：同时选择靶点和入点后可点击确认显示针道
  - 数据保存：保存靶点和入点坐标数据
  - 记录查看：显示最新一次的规划数据

  > 可以做多跟针

#### 2.7.2 操作流程
- **确认条件**：必须同时选择靶点和入点才能点击确认
- **数据策略**：只保留最新的规划数据
- **退出方式**：点击取消返回**==返回到标记页面==**

---

## 3. 非功能需求

### 3.1 可用性要求
- **系统可用性**：99.5%的系统可用时间
- **用户界面**：界面友好，符合医疗软件使用习惯
- **错误处理**：提供清晰的错误提示信息
- **帮助文档**：提供完整的用户操作手册

### 3.2 兼容性要求
- **浏览器支持**：Chrome 90+、Firefox 88+、Safari 14+
- **屏幕分辨率**：支持1920x1080及以上分辨率
- **DICOM标准**：兼容DICOM 3.0标准

---

## 4. 技术约束和依赖

### 4.1 技术栈
- **前端**：React + TypeScript
- **后端**：Python + FastAPI
- **数据库**：MySQL + Redis
- **PACS服务**：Orthanc DICOM服务器
- **影像处理**：Cornerstone3D.js
- **AI服务**：Dify平台集成
- **回调脚本**：Lua脚本（Orthanc集成）

### 4.2 第三方依赖
- **Orthanc**：开源DICOM服务器，提供影像存储和WADO服务
- **Dify平台**：提供AI对话和评估服务
- **TotalSegmentator**：提供自动分割算法
- **Cornerstone3D.js**：提供DICOM影像查看功能
- **JSON.lua**：Lua JSON处理库

### 4.3 系统集成要求
- **API接口**：RESTful API设计
- **数据格式**：JSON数据交换格式
- **DICOM协议**：支持DICOM C-Store和WADO协议
- **回调机制**：基于HTTP POST的异步回调接口

---

## 5. 验收标准

### 5.1 功能验收标准
- **登录系统**：用户能够成功登录并跳转到正确页面
- **DICOM数据接入**：能够通过Orthanc上传DICOM文件并自动回调到系统
- **病例管理**：能够正确显示病例列表和详情，数据来源于DICOM回调
- **AI对话**：能够与AI进行正常对话并保存记录
- **AI评估**：能够提交评估表单并生成PDF报告
- **影像标记**：手动和自动标记功能正常工作
- **手术规划**：能够选择靶点入点并显示针道

### 5.2 性能验收标准
- **加载速度**：所有页面加载时间符合性能要求
- **并发测试**：通过100用户并发测试
- **稳定性测试**：连续运行24小时无崩溃

---

## 6. 项目里程碑

### 6.1 开发阶段
- **第一阶段**（4周）：用户认证和DICOM数据接入系统
- **第二阶段**（4周）：病例管理和影像查看系统
- **第三阶段**（6周）：AI对话和评估系统
- **第四阶段**（8周）：影像标记和手术规划系统
- **第五阶段**（2周）：系统集成和测试

### 6.2 测试阶段
- **单元测试**：开发过程中持续进行
- **集成测试**：各模块完成后进行
- **用户验收测试**：系统完成后进行
- **性能测试**：部署前进行

---

## 7. 风险评估

### 7.1 技术风险
- **AI服务稳定性**：Dify平台服务中断风险
- **算法准确性**：自动分割算法准确性风险
- **影像兼容性**：不同设备DICOM格式兼容性风险

### 7.2 业务风险
- **用户接受度**：医生对新系统的接受程度
- **数据质量**：历史数据质量影响系统效果
- **法规合规**：医疗软件法规合规要求

### 7.3 风险缓解措施
- **备用方案**：为关键第三方服务准备备用方案
- **用户培训**：提供充分的用户培训和支持
- **合规审查**：邀请法规专家进行合规性审查

---

## 8. 附录

### 8.1 术语表
- **DICOM**：医学数字成像和通信标准
- **PACS**：图像归档和通信系统
- **AI**：人工智能
- **CT**：计算机断层扫描
- **NCCT**：非增强CT
- **CTA**：CT血管造影
- **CTP**：CT灌注成像

### 8.2 参考文档
- 原始需求整理文档：诊疗平台-需求整理v0.1.md
- TotalSegmentator算法文档：https://github.com/wasserth/TotalSegmentator
- Dify平台API文档
- OHIF Viewer文档

### 8.3 变更记录
| 版本 | 日期 | 变更内容 | 变更人 |
|------|------|----------|--------|
| v1.0 | 2025-06-17 | 初始版本创建 | 产品团队 |
| v1.1 | 2025-07-07 | 增加DICOM数据接入系统模块，详细说明通过Orthanc PACS系统接收DICOM文件并自动回调到诊疗平台的完整流程 | AI助手 |

---

**文档结束**