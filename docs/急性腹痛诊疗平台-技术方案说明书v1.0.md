# 急性腹痛诊疗平台技术方案说明书

**版本：** v1.0  
**日期：** 2025年7月7日  
**编写人：** 技术团队  
**审核人：** 架构师  

---

## 1. 技术方案概述

### 1.1 方案背景

基于《急性腹痛诊疗平台产品需求说明书v1.1》，本技术方案旨在构建一个高可用、高性能、易扩展的医疗诊疗平台。系统集成AI智能分析、DICOM影像处理、手术规划等核心功能，为医生提供全面的诊疗辅助工具。

### 1.2 技术目标

- **高可用性**：系统可用性达到99.5%以上
- **高性能**：页面响应时间<3秒，影像加载时间<5秒
- **可扩展性**：支持水平扩展，满足业务增长需求
- **安全性**：符合医疗数据安全标准，保护患者隐私
- **标准化**：遵循DICOM 3.0标准，确保设备兼容性

### 1.3 技术原则

- **微服务架构**：模块化设计，便于维护和扩展
- **容器化部署**：使用Docker容器化，提高部署效率
- **API优先**：RESTful API设计，支持前后端分离
- **数据驱动**：基于数据的决策和优化
- **开源优先**：优先选择成熟的开源技术栈

---

## 2. 系统总体架构

### 2.1 架构概览

```
┌─────────────────────────────────────────────────────────────┐
│                        用户层                                │
├─────────────────────────────────────────────────────────────┤
│  Web浏览器 (Chrome/Firefox/Safari)                          │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      前端应用层                              │
├─────────────────────────────────────────────────────────────┤
│  React + TypeScript + Cornerstone3D.js + VTK.js            │
│  ├── 用户认证模块                                            │
│  ├── 病例管理模块                                            │
│  ├── 影像查看模块                                            │
│  ├── AI对话模块                                             │
│  ├── AI评估模块                                             │
│  ├── 影像标记模块                                            │
│  └── 手术规划模块                                            │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      API网关层                               │
├─────────────────────────────────────────────────────────────┤
│  Nginx + 负载均衡 + SSL终端                                  │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      后端服务层                              │
├─────────────────────────────────────────────────────────────┤
│  Python + FastAPI                                          │
│  ├── 用户认证服务                                            │
│  ├── 病例管理服务                                            │
│  ├── DICOM回调服务                                          │
│  ├── AI服务代理                                             │
│  ├── 影像标记服务                                            │
│  └── 手术规划服务                                            │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      数据存储层                              │
├─────────────────────────────────────────────────────────────┤
│  ├── MySQL (业务数据)                                        │
│  ├── Redis (缓存/会话)                                       │
│  └── 文件存储 (标记数据/报告)                                 │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    第三方服务层                              │
├─────────────────────────────────────────────────────────────┤
│  ├── Orthanc PACS (DICOM存储)                               │
│  ├── Dify AI平台 (对话/评估)                                 │
│  └── TotalSegmentator (自动分割)                             │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 技术栈选型

#### 2.2.1 前端技术栈
- **框架**：React 18.x + TypeScript 5.x
- **状态管理**：Redux Toolkit + RTK Query
- **UI组件库**：Ant Design 5.x
- **影像处理**：Cornerstone3D.js + VTK.js
- **构建工具**：Vite 4.x
- **代码质量**：ESLint + Prettier + Husky

#### 2.2.2 后端技术栈
- **框架**：Python 3.11 + FastAPI 0.104.x
- **ORM**：SQLAlchemy 2.x + Alembic
- **认证**：JWT + OAuth2
- **异步处理**：Celery + Redis
- **API文档**：OpenAPI 3.0 (Swagger)
- **代码质量**：Black + isort + mypy

#### 2.2.3 数据库技术栈
- **关系数据库**：MySQL 8.0
- **缓存数据库**：Redis 7.x
- **连接池**：SQLAlchemy连接池
- **数据迁移**：Alembic

#### 2.2.4 基础设施技术栈
- **容器化**：Docker + Docker Compose
- **反向代理**：Nginx 1.24.x
- **PACS服务**：Orthanc 1.12.x
- **监控**：Prometheus + Grafana
- **日志**：ELK Stack (Elasticsearch + Logstash + Kibana)

### 2.3 部署架构

```
┌─────────────────────────────────────────────────────────────┐
│                      生产环境                                │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │   Nginx     │  │   Nginx     │  │   Nginx     │          │
│  │ (负载均衡)   │  │ (负载均衡)   │  │ (负载均衡)   │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
│           │               │               │                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │  Frontend   │  │  Frontend   │  │  Frontend   │          │
│  │ (React App) │  │ (React App) │  │ (React App) │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
│           │               │               │                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │   Backend   │  │   Backend   │  │   Backend   │          │
│  │ (FastAPI)   │  │ (FastAPI)   │  │ (FastAPI)   │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
│           │               │               │                 │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                   MySQL Cluster                        │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │ │
│  │  │   Master    │  │   Slave     │  │   Slave     │     │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘     │ │
│  └─────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                   Redis Cluster                        │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │ │
│  │  │   Master    │  │   Slave     │  │   Slave     │     │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘     │ │
│  └─────────────────────────────────────────────────────────┘ │
│  ┌─────────────┐  ┌─────────────┐                          │
│  │   Orthanc   │  │   Orthanc   │                          │
│  │    PACS     │  │    PACS     │                          │
│  └─────────────┘  └─────────────┘                          │
└─────────────────────────────────────────────────────────────┘
```

---

## 3. 数据库设计

### 3.1 数据库架构

#### 3.1.1 主要实体关系图

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│   Studies   │────▶│   Series    │────▶│  Instances  │
│             │ 1:N │             │ 1:N │             │
│ - id (PK)   │     │ - id (PK)   │     │ - id (PK)   │
│ - study_uid │     │ - study_id  │     │ - series_id │
│ - patient_id│     │   (FK)      │     │   (FK)      │
│ - patient_name    │ - series_uid│     │ - sop_uid   │
│ - patient_age     │ - modality  │     │ - file_path │
│ - patient_gender  │ - created_at│     │ - created_at│
│ - patient_birth   └─────────────┘     └─────────────┘
│ - study_date│
│ - study_desc│              ┌─────────────┐
│ - created_at│              │    Users    │
└─────────────┘              │             │
       │ 1:N                 │ - id (PK)   │
       │                     │ - username  │
       │                     │ - password  │
       │                     │ - role      │
       │                     │ - created_at│
       │                     └─────────────┘
       │                             │
       │                             │ N:M
       ▼                             ▼
┌─────────────┐             ┌─────────────┐
│ Assessments │             │ Annotations │
│             │             │             │
│ - id (PK)   │             │ - id (PK)   │
│ - study_id  │             │ - series_id │
│   (FK)      │             │   (FK)      │
│ - user_id   │             │ - user_id   │
│   (FK)      │             │   (FK)      │
│ - content   │             │ - data      │
│ - ai_result │             │ - label     │
│ - created_at│             │ - created_at│
└─────────────┘             └─────────────┘
```

**关系说明：**
- **Studies → Series (1:N)**：一次检查可以包含多个序列
- **Series → Instances (1:N)**：一个序列包含多个影像实例
- **Studies → Assessments (1:N)**：一次检查可以有多次AI评估
- **Users → Assessments (1:N)**：一个用户可以创建多个评估
- **Series × Users → Annotations (N:M)**：用户可以对多个序列进行标记

**设计优势：**
- **简化架构**：减少表关联，提高查询性能
- **符合业务**：以检查为中心的业务流程
- **数据一致**：患者信息直接来自DICOM，无需额外维护

#### 3.1.2 核心数据表设计

**用户表 (users)**
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('doctor', 'admin') DEFAULT 'doctor',
    full_name VARCHAR(100),
    email VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

**检查表 (studies) - 包含患者信息**
```sql
CREATE TABLE studies (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    study_uid VARCHAR(64) UNIQUE NOT NULL,
    -- 患者信息字段
    patient_id VARCHAR(64) NOT NULL,              -- DICOM Patient ID
    patient_name VARCHAR(100),                    -- 患者姓名
    patient_age INT,                              -- 患者年龄
    patient_gender ENUM('M', 'F', 'O'),           -- 患者性别
    patient_birth_date DATE,                      -- 患者出生日期
    -- 检查信息字段
    study_date DATE,                              -- 检查日期
    study_time TIME,                              -- 检查时间
    study_description TEXT,                       -- 检查描述
    accession_number VARCHAR(16),                 -- 检查号
    status ENUM('receiving', 'completed', 'error') DEFAULT 'receiving',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    -- 索引
    INDEX idx_study_uid (study_uid),
    INDEX idx_patient_id (patient_id),            -- 患者ID索引（用于查询同一患者的多次检查）
    INDEX idx_patient_name (patient_name),        -- 患者姓名索引（用于搜索）
    INDEX idx_study_date (study_date),
    INDEX idx_status (status),
    INDEX idx_patient_study (patient_id, study_date)  -- 复合索引（患者+日期）
);
```

**序列表 (series)**
```sql
CREATE TABLE series (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    series_uid VARCHAR(64) UNIQUE NOT NULL,
    study_id BIGINT NOT NULL,
    series_number INT,
    modality VARCHAR(16),
    series_description TEXT,
    body_part_examined VARCHAR(16),
    slice_thickness DECIMAL(10,6),
    image_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (study_id) REFERENCES studies(id) ON DELETE CASCADE,
    INDEX idx_series_uid (series_uid),
    INDEX idx_modality (modality)
);
```

**实例表 (instances)**
```sql
CREATE TABLE instances (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    sop_instance_uid VARCHAR(64) UNIQUE NOT NULL,
    series_id BIGINT NOT NULL,
    instance_number INT,
    file_path VARCHAR(500),
    file_size BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (series_id) REFERENCES series(id) ON DELETE CASCADE,
    INDEX idx_sop_instance_uid (sop_instance_uid),
    INDEX idx_instance_number (instance_number)
);
```

**AI评估表 (assessments) - 每个用户对每个检查只保存最新评估**
```sql
CREATE TABLE assessments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    study_id BIGINT NOT NULL,                     -- 外键指向studies表
    user_id BIGINT NOT NULL,                      -- 评估用户ID
    assessment_data JSON,                         -- 评估表单数据（症状、病史等）
    ai_result JSON,                               -- AI分析结果
    report_path VARCHAR(500),                     -- PDF报告文件路径
    version INT DEFAULT 1,                        -- 评估版本号（用于追踪修改次数）
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (study_id) REFERENCES studies(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_study_user (study_id, user_id), -- 确保每个用户对每个检查只有一个评估
    INDEX idx_study_id (study_id),                -- 查询某检查的所有评估
    INDEX idx_user_id (user_id),                  -- 查询某用户的所有评估
    INDEX idx_updated_at (updated_at)             -- 按更新时间排序
);
```

**标记数据表 (annotations)**
```sql
CREATE TABLE annotations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    series_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    annotation_type ENUM('manual', 'auto') DEFAULT 'manual',
    annotation_data JSON,
    label_name VARCHAR(100),
    label_color VARCHAR(7),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (series_id) REFERENCES series(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_series_user (series_id, user_id),
    INDEX idx_annotation_type (annotation_type)
);
```

**手术规划表 (surgical_plans)**
```sql
CREATE TABLE surgical_plans (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    series_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    target_point JSON,
    entry_point JSON,
    needle_path JSON,
    plan_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (series_id) REFERENCES series(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_series_user (series_id, user_id)
);
```

**对话记录表 (chat_sessions) - 检查+用户维度的对话**
```sql
CREATE TABLE chat_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    study_id BIGINT NOT NULL,                     -- 检查ID
    user_id BIGINT NOT NULL,                      -- 用户ID
    local_session_id VARCHAR(64) UNIQUE NOT NULL, -- 本地对话ID
    dify_conversation_id VARCHAR(64),             -- Dify平台的对话ID
    conversation_data JSON,                       -- 对话历史数据
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (study_id) REFERENCES studies(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_study_user (study_id, user_id), -- 每个用户对每个检查只有一个对话
    INDEX idx_local_session (local_session_id),
    INDEX idx_dify_conversation (dify_conversation_id)
);
```

### 3.2 Redis缓存设计

#### 3.2.1 缓存策略
- **用户会话**：`session:{user_id}` (TTL: 24小时)
- **DICOM元数据**：`dicom:{study_uid}` (TTL: 1小时)
- **AI评估结果**：`assessment:{study_id}:{user_id}` (TTL: 30分钟)
- **AI对话历史**：`chat:{study_id}:{user_id}` (TTL: 2小时)
- **标记数据**：`annotation:{series_id}:{user_id}` (TTL: 30分钟)

#### 3.2.2 缓存键设计
```
# 用户会话
session:user:123 -> {user_info, permissions, last_access}

# DICOM元数据缓存
dicom:study:*******.5 -> {patient_info, study_info, series_list}

# AI评估缓存
assessment:study:456:user:123 -> {assessment_data, ai_result}

# AI对话历史缓存
chat:study:456:user:123 -> {local_session_id, dify_conversation_id, messages}

# 标记数据缓存
annotation:series:789:user:123 -> {annotation_list, labels}

# 患者检查历史缓存
patient:history:P001 -> {study_list, latest_study}

# 任务队列
task:segmentation:series:789 -> {task_id, status, progress}
```

---

## 4. API接口设计

### 4.1 API设计原则

- **RESTful风格**：遵循REST架构风格
- **统一响应格式**：标准化的JSON响应格式
- **版本控制**：API版本化管理 (/api/v1/)
- **认证授权**：JWT Token认证
- **错误处理**：统一的错误码和错误信息

### 4.2 API响应格式

#### 4.2.1 成功响应格式
```json
{
    "code": 200,
    "message": "success",
    "data": {
        // 具体数据内容
    },
    "timestamp": "2025-07-07T10:30:00Z"
}
```

#### 4.2.2 错误响应格式
```json
{
    "code": 400,
    "message": "参数错误",
    "error": "详细错误信息",
    "timestamp": "2025-07-07T10:30:00Z"
}
```

### 4.3 核心API接口

#### 4.3.1 用户认证接口

**登录接口**
```
POST /api/v1/auth/login
Content-Type: application/json

Request:
{
    "username": "doctor001",
    "password": "password123"
}

Response:
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "token_type": "bearer",
        "expires_in": 86400,
        "user_info": {
            "id": 1,
            "username": "doctor001",
            "full_name": "张医生",
            "role": "doctor"
        }
    }
}
```

**刷新Token接口**
```
POST /api/v1/auth/refresh
Authorization: Bearer {access_token}

Response:
{
    "code": 200,
    "message": "Token刷新成功",
    "data": {
        "access_token": "new_token_here",
        "expires_in": 86400
    }
}
```

#### 4.3.2 病例管理接口

**获取病例列表**
```
GET /api/v1/cases?page=1&size=20&search=张三
Authorization: Bearer {access_token}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "total": 100,
        "page": 1,
        "size": 20,
        "items": [
            {
                "id": 1,
                "patient_id": "P001",
                "patient_name": "张三",
                "patient_age": 45,
                "patient_gender": "M",
                "study_date": "2025-07-07",
                "modality": "CT",
                "status": "completed",
                "created_at": "2025-07-07T10:30:00Z"
            }
        ]
    }
}
```

**获取病例详情**
```
GET /api/v1/cases/{case_id}
Authorization: Bearer {access_token}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "id": 1,
        "study_uid": "*******.5",
        "patient_id": "P001",
        "patient_name": "张三",
        "patient_age": 45,
        "patient_gender": "M",
        "patient_birth_date": "1980-01-01",
        "study_date": "2025-07-07",
        "study_time": "10:30:00",
        "study_description": "腹部CT平扫",
        "accession_number": "ACC001",
        "status": "completed",
        "series_list": [
            {
                "series_uid": "*******.5.1",
                "series_number": 1,
                "modality": "CT",
                "series_description": "Axial",
                "image_count": 120,
                "slice_thickness": 1.25
            }
        ]
    }
}
```

#### 4.3.3 DICOM回调接口

**影像实例回调**
```
POST /api/v1/images/callback/
Content-Type: application/json

Request:
{
    "orthanc_id": "12345678-1234-1234-1234-123456789012",
    "sop_instance_uid": "*******.5.6.7",
    "series_uid": "*******.5.6",
    "study_uid": "*******.5",
    "patient_id": "P001",
    "modality": "CT",
    "instance_number": 1
}

Response:
{
    "code": 200,
    "message": "回调处理成功"
}
```

**序列稳定回调**
```
POST /api/v1/series/callback/
Content-Type: application/json

Request:
{
    "orthanc_id": "12345678-1234-1234-1234-123456789012",
    "series_uid": "*******.5.6",
    "study_uid": "*******.5",
    "patient_id": "P001",
    "modality": "CT",
    "series_number": 1,
    "image_count": 120
}

Response:
{
    "code": 200,
    "message": "序列处理完成"
}
```

#### 4.3.4 AI服务接口

**AI对话接口**
```
POST /api/v1/ai/chat
Authorization: Bearer {access_token}
Content-Type: application/json

Request:
{
    "study_id": 1,                                -- 检查ID
    "message": "患者腹痛症状，CT显示什么？",
    "context": {                                  -- 可选的上下文信息
        "patient_name": "张三",
        "patient_age": 45,
        "study_description": "腹部CT平扫"
    }
}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "response": "根据CT影像显示...",
        "local_session_id": "study_1_user_123",   -- 本地对话ID
        "dify_conversation_id": "conv_456",       -- Dify对话ID
        "message_id": "msg_789"                   -- 消息ID
    }
}
```

**获取对话历史接口**
```
GET /api/v1/ai/chat/history?study_id=1
Authorization: Bearer {access_token}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "local_session_id": "study_1_user_123",
        "dify_conversation_id": "conv_456",
        "messages": [
            {
                "role": "user",
                "content": "患者腹痛症状，CT显示什么？",
                "timestamp": "2025-07-07T10:30:00Z"
            },
            {
                "role": "assistant",
                "content": "根据CT影像显示...",
                "timestamp": "2025-07-07T10:30:05Z"
            }
        ],
        "created_at": "2025-07-07T10:00:00Z",
        "updated_at": "2025-07-07T10:30:05Z"
    }
}
```

**提交AI评估接口（新建或更新）**
```
POST /api/v1/ai/assessment
Authorization: Bearer {access_token}
Content-Type: application/json

Request:
{
    "study_id": 1,                                -- 检查ID
    "assessment_data": {
        "patient_name": "张三",                   -- 从study中自动获取
        "patient_age": 45,
        "patient_gender": "M",
        "symptoms": "右下腹痛",
        "duration": "6小时",
        "severity": "中度",
        "associated_symptoms": ["恶心", "呕吐"]
    }
}

Response:
{
    "code": 200,
    "message": "评估完成（已覆盖之前的评估）",
    "data": {
        "assessment_id": 123,
        "is_update": true,                        -- 是否为更新操作
        "version": 2,                             -- 评估版本号
        "ai_result": {
            "diagnosis": "急性阑尾炎可能性大",
            "confidence": 0.85,
            "recommendations": ["建议进一步检查", "考虑手术治疗"]
        },
        "report_url": "/api/v1/reports/123/download"
    }
}
```

**获取已有评估接口（用于编辑）**
```
GET /api/v1/ai/assessment?study_id=1
Authorization: Bearer {access_token}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "assessment_id": 123,
        "study_id": 1,
        "assessment_data": {
            "patient_name": "张三",
            "patient_age": 45,
            "patient_gender": "M",
            "symptoms": "右下腹痛",
            "duration": "6小时",
            "severity": "中度",
            "associated_symptoms": ["恶心", "呕吐"]
        },
        "ai_result": {
            "diagnosis": "急性阑尾炎可能性大",
            "confidence": 0.85,
            "recommendations": ["建议进一步检查", "考虑手术治疗"]
        },
        "version": 2,
        "updated_at": "2025-07-07T15:30:00Z"
    }
}
```

#### 4.3.5 影像标记接口

**保存标记数据**
```
POST /api/v1/annotations
Authorization: Bearer {access_token}
Content-Type: application/json

Request:
{
    "series_id": 1,
    "annotation_type": "manual",
    "label_name": "病灶区域",
    "label_color": "#FF0000",
    "annotation_data": {
        "contours": [
            {
                "slice_index": 50,
                "points": [[100, 100], [150, 100], [150, 150], [100, 150]]
            }
        ]
    }
}

Response:
{
    "code": 200,
    "message": "标记保存成功",
    "data": {
        "annotation_id": 456
    }
}
```

**获取标记数据**
```
GET /api/v1/annotations?series_id=1
Authorization: Bearer {access_token}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "annotations": [
            {
                "id": 456,
                "annotation_type": "manual",
                "label_name": "病灶区域",
                "label_color": "#FF0000",
                "annotation_data": {...},
                "created_at": "2025-07-07T10:30:00Z"
            }
        ]
    }
}
```

#### 4.3.6 自动分割接口

**启动自动分割**
```
POST /api/v1/segmentation/start
Authorization: Bearer {access_token}
Content-Type: application/json

Request:
{
    "series_id": 1,
    "algorithm": "totalsegmentator"
}

Response:
{
    "code": 200,
    "message": "分割任务已启动",
    "data": {
        "task_id": "task_789",
        "status": "running"
    }
}
```

**查询分割进度**
```
GET /api/v1/segmentation/status/{task_id}
Authorization: Bearer {access_token}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "task_id": "task_789",
        "status": "running",
        "progress": 65,
        "estimated_time": 120
    }
}
```

#### 4.3.7 手术规划接口

**保存手术规划**
```
POST /api/v1/surgical-plans
Authorization: Bearer {access_token}
Content-Type: application/json

Request:
{
    "series_id": 1,
    "target_point": {"x": 100, "y": 150, "z": 75},
    "entry_point": {"x": 50, "y": 100, "z": 25},
    "needle_path": {
        "points": [
            {"x": 50, "y": 100, "z": 25},
            {"x": 75, "y": 125, "z": 50},
            {"x": 100, "y": 150, "z": 75}
        ]
    }
}

Response:
{
    "code": 200,
    "message": "手术规划保存成功",
    "data": {
        "plan_id": 789
    }
}
```

---

## 5. 前端技术架构

### 5.1 前端架构设计

#### 5.1.1 项目结构
```
src/
├── components/          # 通用组件
│   ├── Layout/         # 布局组件
│   ├── Auth/           # 认证组件
│   ├── Common/         # 通用UI组件
│   └── Medical/        # 医疗专用组件
├── pages/              # 页面组件
│   ├── Login/          # 登录页
│   ├── CaseList/       # 病例列表页
│   ├── CaseDetail/     # 病例详情页
│   ├── AIChat/         # AI对话页
│   ├── Assessment/     # AI评估页
│   ├── Annotation/     # 影像标记页
│   └── SurgicalPlan/   # 手术规划页
├── hooks/              # 自定义Hooks
├── services/           # API服务
├── store/              # Redux状态管理
├── utils/              # 工具函数
├── types/              # TypeScript类型定义
└── assets/             # 静态资源
```

#### 5.1.2 状态管理架构
```typescript
// store/index.ts
import { configureStore } from '@reduxjs/toolkit'
import { authApi } from './api/authApi'
import { caseApi } from './api/caseApi'
import { aiApi } from './api/aiApi'
import authSlice from './slices/authSlice'
import uiSlice from './slices/uiSlice'

export const store = configureStore({
  reducer: {
    auth: authSlice,
    ui: uiSlice,
    [authApi.reducerPath]: authApi.reducer,
    [caseApi.reducerPath]: caseApi.reducer,
    [aiApi.reducerPath]: aiApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(
      authApi.middleware,
      caseApi.middleware,
      aiApi.middleware
    ),
})
```

#### 5.1.3 路由设计
```typescript
// router/index.tsx
import { createBrowserRouter } from 'react-router-dom'
import { ProtectedRoute } from '@/components/Auth/ProtectedRoute'

export const router = createBrowserRouter([
  {
    path: '/login',
    element: <LoginPage />
  },
  {
    path: '/',
    element: <ProtectedRoute><Layout /></ProtectedRoute>,
    children: [
      {
        index: true,
        element: <CaseListPage />
      },
      {
        path: 'cases/:caseId',
        element: <CaseDetailPage />
      },
      {
        path: 'cases/:caseId/assessment',
        element: <AssessmentPage />
      },
      {
        path: 'cases/:caseId/annotation',
        element: <AnnotationPage />
      },
      {
        path: 'cases/:caseId/surgical-plan',
        element: <SurgicalPlanPage />
      }
    ]
  }
])
```

### 5.2 影像处理组件设计

#### 5.2.1 DICOM查看器组件
```typescript
// components/Medical/DicomViewer.tsx
import { useEffect, useRef } from 'react'
import { RenderingEngine, Enums } from '@cornerstonejs/core'
import { cornerstoneStreamingImageVolumeLoader } from '@cornerstonejs/streaming-image-volume-loader'

interface DicomViewerProps {
  seriesId: string
  onImageLoad?: (imageData: any) => void
  annotations?: AnnotationData[]
}

export const DicomViewer: React.FC<DicomViewerProps> = ({
  seriesId,
  onImageLoad,
  annotations
}) => {
  const viewportRef = useRef<HTMLDivElement>(null)
  const renderingEngineRef = useRef<RenderingEngine>()

  useEffect(() => {
    if (!viewportRef.current) return

    // 初始化渲染引擎
    const renderingEngine = new RenderingEngine('myRenderingEngine')
    renderingEngineRef.current = renderingEngine

    // 创建视口
    const viewportInput = {
      viewportId: 'CT_AXIAL',
      type: Enums.ViewportType.ORTHOGRAPHIC,
      element: viewportRef.current,
      defaultOptions: {
        orientation: Enums.OrientationAxis.AXIAL,
      },
    }

    renderingEngine.enableElement(viewportInput)

    // 加载影像数据
    loadImageData(seriesId)

    return () => {
      renderingEngine.destroy()
    }
  }, [seriesId])

  const loadImageData = async (seriesId: string) => {
    try {
      // 从Orthanc获取DICOM数据
      const imageIds = await fetchImageIds(seriesId)

      // 创建体积数据
      const volume = await cornerstoneStreamingImageVolumeLoader.createAndCacheVolume(
        `cornerstoneStreamingImageVolume:${seriesId}`,
        { imageIds }
      )

      // 设置视口
      const viewport = renderingEngineRef.current?.getViewport('CT_AXIAL')
      if (viewport) {
        await viewport.setVolumes([{ volumeId: volume.volumeId }])
        viewport.render()
      }

      onImageLoad?.(volume)
    } catch (error) {
      console.error('Failed to load DICOM data:', error)
    }
  }

  return (
    <div className="dicom-viewer">
      <div ref={viewportRef} className="viewport" style={{ width: '100%', height: '500px' }} />
      {/* 工具栏 */}
      <div className="toolbar">
        <button onClick={() => resetView()}>重置</button>
        <button onClick={() => toggleAnnotations()}>标记</button>
      </div>
    </div>
  )
}
```

#### 5.2.2 3D渲染组件
```typescript
// components/Medical/VTKViewer.tsx
import { useEffect, useRef } from 'react'
import vtkFullScreenRenderWindow from '@kitware/vtk.js/Rendering/Misc/FullScreenRenderWindow'
import vtkVolumeMapper from '@kitware/vtk.js/Rendering/Core/VolumeMapper'
import vtkVolume from '@kitware/vtk.js/Rendering/Core/Volume'

interface VTKViewerProps {
  volumeData: ArrayBuffer
  annotations?: AnnotationData[]
}

export const VTKViewer: React.FC<VTKViewerProps> = ({ volumeData, annotations }) => {
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!containerRef.current || !volumeData) return

    // 创建渲染窗口
    const fullScreenRenderer = vtkFullScreenRenderWindow.newInstance({
      rootContainer: containerRef.current,
    })

    const renderer = fullScreenRenderer.getRenderer()
    const renderWindow = fullScreenRenderer.getRenderWindow()

    // 创建体积映射器
    const mapper = vtkVolumeMapper.newInstance()
    mapper.setInputData(volumeData)

    // 创建体积对象
    const volume = vtkVolume.newInstance()
    volume.setMapper(mapper)

    // 添加到渲染器
    renderer.addVolume(volume)
    renderer.resetCamera()
    renderWindow.render()

    return () => {
      fullScreenRenderer.delete()
    }
  }, [volumeData])

  return (
    <div ref={containerRef} className="vtk-viewer" style={{ width: '100%', height: '500px' }} />
  )
}
```

### 5.3 AI集成组件

#### 5.3.1 AI对话组件
```typescript
// components/AI/ChatWidget.tsx
import { useState, useRef, useEffect } from 'react'
import { useSendMessageMutation, useGetChatHistoryQuery } from '@/store/api/aiApi'

interface ChatWidgetProps {
  studyId: number
  studyInfo: StudyInfo
}

export const ChatWidget: React.FC<ChatWidgetProps> = ({ studyId, studyInfo }) => {
  const [isOpen, setIsOpen] = useState(false)
  const [message, setMessage] = useState('')

  const messagesEndRef = useRef<HTMLDivElement>(null)

  const [sendMessage, { isLoading }] = useSendMessageMutation()
  const { data: chatHistory } = useGetChatHistoryQuery({ study_id: studyId })

  const handleSendMessage = async () => {
    if (!message.trim()) return

    try {
      await sendMessage({
        study_id: studyId,
        message,
        context: {
          patient_name: studyInfo.patient_name,
          patient_age: studyInfo.patient_age,
          study_description: studyInfo.study_description
        }
      }).unwrap()

      setMessage('')
    } catch (error) {
      console.error('Failed to send message:', error)
    }
  }

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [chatHistory])

  return (
    <>
      {/* 悬浮按钮 */}
      <div className="chat-fab" onClick={() => setIsOpen(true)}>
        <Icon type="message" />
      </div>

      {/* 对话窗口 */}
      {isOpen && (
        <div className="chat-widget">
          <div className="chat-header">
            <span>AI医疗助手</span>
            <button onClick={() => setIsOpen(false)}>×</button>
          </div>

          <div className="chat-messages">
            {chatHistory?.messages.map((msg, index) => (
              <div key={index} className={`message ${msg.role}`}>
                <div className="content">{msg.content}</div>
                <div className="timestamp">{msg.timestamp}</div>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>

          <div className="chat-input">
            <input
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
              placeholder="输入您的问题..."
              disabled={isLoading}
            />
            <button onClick={handleSendMessage} disabled={isLoading}>
              发送
            </button>
          </div>
        </div>
      )}
    </>
  )
}
```

#### 5.3.2 AI评估组件
```typescript
// components/AI/AssessmentForm.tsx
import { useState, useEffect } from 'react'
import { Form, Input, Select, Button, Card, Alert } from 'antd'
import { useSubmitAssessmentMutation, useGetAssessmentQuery } from '@/store/api/aiApi'

interface AssessmentFormProps {
  studyId: number
  studyInfo: StudyInfo
  onAssessmentComplete: (result: AssessmentResult) => void
}

export const AssessmentForm: React.FC<AssessmentFormProps> = ({
  studyId,
  studyInfo,
  onAssessmentComplete
}) => {
  const [form] = Form.useForm()
  const [submitAssessment, { isLoading }] = useSubmitAssessmentMutation()

  // 获取已有评估（如果存在）
  const { data: existingAssessment } = useGetAssessmentQuery({ study_id: studyId })

  useEffect(() => {
    // 如果存在已有评估，填充表单
    if (existingAssessment?.exists) {
      form.setFieldsValue(existingAssessment.assessment_data)
    } else {
      // 否则填充患者基本信息
      form.setFieldsValue({
        patient_name: studyInfo.patient_name,
        patient_age: studyInfo.patient_age,
        patient_gender: studyInfo.patient_gender
      })
    }
  }, [existingAssessment, studyInfo, form])

  const handleSubmit = async (values: any) => {
    try {
      const result = await submitAssessment({
        study_id: studyId,
        assessment_data: {
          ...values,
          patient_name: studyInfo.patient_name,
          patient_age: studyInfo.patient_age,
          patient_gender: studyInfo.patient_gender
        }
      }).unwrap()

      onAssessmentComplete(result)
    } catch (error) {
      console.error('Assessment failed:', error)
    }
  }

  return (
    <Card title="AI智能评估" className="assessment-form">
      {/* 显示是否为编辑模式 */}
      {existingAssessment?.exists && (
        <Alert
          message="编辑模式"
          description={`正在编辑已有评估（版本 ${existingAssessment.version}），提交后将覆盖之前的结果`}
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
      >
        <Form.Item label="患者姓名" name="patient_name">
          <Input disabled />
        </Form.Item>

        <Form.Item label="年龄" name="patient_age">
          <Input disabled />
        </Form.Item>

        <Form.Item label="性别" name="patient_gender">
          <Select disabled>
            <Select.Option value="M">男</Select.Option>
            <Select.Option value="F">女</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item label="主要症状" name="symptoms" rules={[{ required: true }]}>
          <Input.TextArea rows={3} placeholder="请描述患者的主要症状" />
        </Form.Item>

        <Form.Item label="症状持续时间" name="duration" rules={[{ required: true }]}>
          <Select placeholder="请选择症状持续时间">
            <Select.Option value="<1h">少于1小时</Select.Option>
            <Select.Option value="1-6h">1-6小时</Select.Option>
            <Select.Option value="6-24h">6-24小时</Select.Option>
            <Select.Option value=">24h">超过24小时</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item label="疼痛程度" name="pain_level">
          <Select placeholder="请选择疼痛程度">
            <Select.Option value="mild">轻度</Select.Option>
            <Select.Option value="moderate">中度</Select.Option>
            <Select.Option value="severe">重度</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item label="伴随症状" name="associated_symptoms">
          <Select mode="multiple" placeholder="请选择伴随症状">
            <Select.Option value="nausea">恶心</Select.Option>
            <Select.Option value="vomiting">呕吐</Select.Option>
            <Select.Option value="fever">发热</Select.Option>
            <Select.Option value="diarrhea">腹泻</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" loading={isLoading} block>
            提交评估
          </Button>
        </Form.Item>
      </Form>
    </Card>
  )
}
```

---

## 6. DICOM集成方案

### 6.1 Orthanc PACS集成架构

#### 6.1.1 Orthanc配置
```json
{
  "Name": "急性腹痛诊疗平台PACS",
  "HttpPort": 8042,
  "DicomPort": 4242,
  "DicomAet": "ABDOMEN_PACS",
  "DicomCheckCalledAet": false,
  "DicomCheckModalityHost": false,
  "RemoteAccessAllowed": true,
  "AuthenticationEnabled": false,
  "SslEnabled": false,
  "StorageDirectory": "/var/lib/orthanc/db",
  "IndexDirectory": "/var/lib/orthanc/db",
  "StorageCompression": false,
  "MaximumStorageSize": 0,
  "MaximumPatientCount": 0,
  "LuaScripts": ["/etc/orthanc/callback.lua"],
  "Plugins": ["/usr/share/orthanc/plugins"],
  "DicomModalities": {},
  "OrthancPeers": {},
  "HttpTimeout": 60,
  "HttpsVerifyPeers": true,
  "UserMetadata": {},
  "DefaultEncoding": "Latin1",
  "AcceptedTransferSyntaxes": ["1.2.840.10008.1.2*"],
  "DeflatedTransferSyntaxAccepted": true,
  "JpegTransferSyntaxAccepted": true,
  "Jpeg2000TransferSyntaxAccepted": true,
  "JpegLosslessTransferSyntaxAccepted": true,
  "JpipTransferSyntaxAccepted": true,
  "Mpeg2TransferSyntaxAccepted": true,
  "RleTransferSyntaxAccepted": true
}
```

#### 6.1.2 Lua回调脚本设计
```lua
-- /etc/orthanc/callback.lua
local json = require('json')

-- 配置回调服务器地址
local CALLBACK_HOST = os.getenv("Lua_Script_CallBack_IP") or "***********"
local CALLBACK_PORT = os.getenv("Lua_Script_CallBack_PORT") or "8000"
local CALLBACK_BASE_URL = "http://" .. CALLBACK_HOST .. ":" .. CALLBACK_PORT

-- 日志函数
function LogInfo(message)
    print("[INFO] " .. os.date("%Y-%m-%d %H:%M:%S") .. " - " .. message)
end

function LogError(message)
    print("[ERROR] " .. os.date("%Y-%m-%d %H:%M:%S") .. " - " .. message)
end

-- 检查是否为有效的医学影像
function IsValidMedicalImage(tags)
    local modality = tags["Modality"]
    local manufacturer = tags["Manufacturer"] or ""

    -- 只处理CT和MR影像
    if modality ~= "CT" and modality ~= "MR" then
        return false
    end

    -- 忽略算法生成的结果
    if string.find(manufacturer, "UNIONSTRONG") then
        return false
    end

    return true
end

-- 发送HTTP POST请求
function SendHttpPost(url, data)
    local json_data = json.encode(data)

    LogInfo("Sending POST request to: " .. url)
    LogInfo("Data: " .. json_data)

    local response = HttpPost(url, json_data, {
        ["Content-Type"] = "application/json",
        ["Accept"] = "application/json"
    })

    if response then
        LogInfo("Response received: " .. response)
        return true
    else
        LogError("Failed to send HTTP request to: " .. url)
        return false
    end
end

-- 实例存储回调
function OnStoredInstance(instanceId, tags, metadata, origin)
    LogInfo("OnStoredInstance triggered for: " .. instanceId)

    -- 检查是否为有效医学影像
    if not IsValidMedicalImage(tags) then
        LogInfo("Skipping non-medical image: " .. instanceId)
        return
    end

    -- 构造回调数据
    local callbackData = {
        orthanc_id = instanceId,
        sop_instance_uid = tags["SOPInstanceUID"],
        series_uid = tags["SeriesInstanceUID"],
        study_uid = tags["StudyInstanceUID"],
        patient_id = tags["PatientID"],
        patient_name = tags["PatientName"],
        patient_age = tags["PatientAge"],
        patient_gender = tags["PatientSex"],
        patient_birth_date = tags["PatientBirthDate"],
        modality = tags["Modality"],
        instance_number = tonumber(tags["InstanceNumber"]) or 0,
        acquisition_date = tags["AcquisitionDate"],
        acquisition_time = tags["AcquisitionTime"],
        slice_thickness = tonumber(tags["SliceThickness"]) or 0,
        pixel_spacing = tags["PixelSpacing"],
        image_orientation = tags["ImageOrientationPatient"],
        image_position = tags["ImagePositionPatient"],
        rows = tonumber(tags["Rows"]) or 0,
        columns = tonumber(tags["Columns"]) or 0,
        bits_allocated = tonumber(tags["BitsAllocated"]) or 0,
        bits_stored = tonumber(tags["BitsStored"]) or 0,
        window_center = tags["WindowCenter"],
        window_width = tags["WindowWidth"],
        rescale_intercept = tonumber(tags["RescaleIntercept"]) or 0,
        rescale_slope = tonumber(tags["RescaleSlope"]) or 1
    }

    -- 发送回调请求
    local url = CALLBACK_BASE_URL .. "/api/v1/images/callback/"
    SendHttpPost(url, callbackData)
end

-- 序列稳定回调
function OnStableSeries(seriesId, tags, metadata)
    LogInfo("OnStableSeries triggered for: " .. seriesId)

    -- 检查是否为有效医学影像
    if not IsValidMedicalImage(tags) then
        LogInfo("Skipping non-medical series: " .. seriesId)
        return
    end

    -- 获取序列中的实例数量
    local instances = RestApiGet("/series/" .. seriesId .. "/instances")
    local instanceCount = 0
    if instances then
        local instanceList = json.decode(instances)
        instanceCount = #instanceList
    end

    -- 构造回调数据
    local callbackData = {
        orthanc_id = seriesId,
        series_uid = tags["SeriesInstanceUID"],
        study_uid = tags["StudyInstanceUID"],
        patient_id = tags["PatientID"],
        modality = tags["Modality"],
        series_number = tonumber(tags["SeriesNumber"]) or 0,
        series_description = tags["SeriesDescription"],
        body_part_examined = tags["BodyPartExamined"],
        slice_thickness = tonumber(tags["SliceThickness"]) or 0,
        image_count = instanceCount,
        acquisition_date = tags["AcquisitionDate"],
        acquisition_time = tags["AcquisitionTime"]
    }

    -- 发送回调请求
    local url = CALLBACK_BASE_URL .. "/api/v1/series/callback/"
    SendHttpPost(url, callbackData)
end

-- 检查稳定回调
function OnStableStudy(studyId, tags, metadata)
    LogInfo("OnStableStudy triggered for: " .. studyId)

    -- 获取检查中的序列数量
    local series = RestApiGet("/studies/" .. studyId .. "/series")
    local seriesCount = 0
    if series then
        local seriesList = json.decode(series)
        seriesCount = #seriesList
    end

    -- 构造回调数据
    local callbackData = {
        orthanc_id = studyId,
        study_uid = tags["StudyInstanceUID"],
        patient_id = tags["PatientID"],
        study_date = tags["StudyDate"],
        study_time = tags["StudyTime"],
        study_description = tags["StudyDescription"],
        accession_number = tags["AccessionNumber"],
        series_count = seriesCount
    }

    -- 发送回调请求
    local url = CALLBACK_BASE_URL .. "/api/v1/studies/callback/"
    SendHttpPost(url, callbackData)
end

LogInfo("Orthanc Lua callback script loaded successfully")
```

### 6.2 DICOM数据流程

#### 6.2.1 数据接收流程
```
1. DICOM设备/客户端 → Orthanc PACS (C-Store)
2. Orthanc验证DICOM格式
3. Orthanc存储DICOM文件
4. Lua脚本触发OnStoredInstance回调
5. 回调API接口处理实例数据
6. 更新数据库记录
7. 前端界面实时更新
```

#### 6.2.2 序列稳定检测
```
1. Orthanc检测序列60秒内无新实例
2. 触发OnStableSeries回调
3. 回调API更新序列状态为"完成"
4. 触发自动分割任务（可选）
5. 前端显示序列完整状态
```

### 6.3 WADO服务集成

#### 6.3.1 影像获取接口
```typescript
// services/dicomService.ts
export class DicomService {
  private orthancBaseUrl = process.env.REACT_APP_ORTHANC_URL || 'http://localhost:8042'

  // 获取影像ID列表
  async getImageIds(seriesId: string): Promise<string[]> {
    const response = await fetch(`${this.orthancBaseUrl}/series/${seriesId}/instances`)
    const instances = await response.json()

    return instances.map((instance: any) =>
      `wadouri:${this.orthancBaseUrl}/instances/${instance.ID}/file`
    )
  }

  // 获取DICOM元数据
  async getDicomMetadata(instanceId: string): Promise<any> {
    const response = await fetch(`${this.orthancBaseUrl}/instances/${instanceId}/tags`)
    return response.json()
  }

  // 获取影像缩略图
  async getThumbnail(instanceId: string, size: number = 256): Promise<string> {
    return `${this.orthancBaseUrl}/instances/${instanceId}/preview?size=${size}`
  }

  // 下载DICOM文件
  async downloadDicom(instanceId: string): Promise<Blob> {
    const response = await fetch(`${this.orthancBaseUrl}/instances/${instanceId}/file`)
    return response.blob()
  }
}
```

---

## 7. AI服务集成方案

### 7.1 Dify平台集成

#### 7.1.1 Dify API配置
```python
# services/dify_service.py
import httpx
from typing import Dict, Any, Optional
from config import settings

class DifyService:
    def __init__(self):
        self.base_url = settings.DIFY_API_URL
        self.api_key = settings.DIFY_API_KEY
        self.chat_workflow_id = settings.DIFY_CHAT_WORKFLOW_ID
        self.assessment_workflow_id = settings.DIFY_ASSESSMENT_WORKFLOW_ID

    async def send_chat_message(
        self,
        message: str,
        user_id: str,
        conversation_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """发送聊天消息到Dify"""

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        payload = {
            "inputs": {
                "message": message,
                "context": context or {}
            },
            "query": message,
            "response_mode": "streaming",
            "user": user_id,
            "conversation_id": conversation_id
        }

        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/chat-messages",
                headers=headers,
                json=payload,
                timeout=30.0
            )
            response.raise_for_status()
            return response.json()

    async def submit_assessment(
        self,
        assessment_data: Dict[str, Any],
        user_id: str
    ) -> Dict[str, Any]:
        """提交AI评估请求"""

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        payload = {
            "inputs": assessment_data,
            "response_mode": "blocking",
            "user": user_id
        }

        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/workflows/{self.assessment_workflow_id}/run",
                headers=headers,
                json=payload,
                timeout=60.0
            )
            response.raise_for_status()
            return response.json()

    async def get_conversation_history(
        self,
        conversation_id: str,
        user_id: str
    ) -> Dict[str, Any]:
        """获取对话历史"""

        headers = {
            "Authorization": f"Bearer {self.api_key}"
        }

        params = {
            "user": user_id,
            "limit": 50
        }

        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/conversations/{conversation_id}/messages",
                headers=headers,
                params=params
            )
            response.raise_for_status()
            return response.json()
```

#### 7.1.2 对话管理服务
```python
# services/chat_service.py
from sqlalchemy.orm import Session
from models.chat import ChatSession
from services.dify_service import DifyService
from datetime import datetime
import uuid

class ChatService:
    def __init__(self, db: Session):
        self.db = db
        self.dify_service = DifyService()

    def generate_local_session_id(self, study_id: int, user_id: int) -> str:
        """生成本地对话ID"""
        return f"study_{study_id}_user_{user_id}"

    async def get_or_create_chat_session(
        self,
        study_id: int,
        user_id: int
    ) -> ChatSession:
        """获取或创建对话会话"""

        # 查找已有会话
        session = self.db.query(ChatSession).filter(
            ChatSession.study_id == study_id,
            ChatSession.user_id == user_id
        ).first()

        if session:
            return session

        # 创建新会话
        local_session_id = self.generate_local_session_id(study_id, user_id)
        session = ChatSession(
            study_id=study_id,
            user_id=user_id,
            local_session_id=local_session_id,
            dify_conversation_id=None,  # 首次对话时为空，发送消息时创建
            conversation_data={"messages": []}
        )

        self.db.add(session)
        self.db.commit()
        self.db.refresh(session)
        return session

    async def send_message(
        self,
        study_id: int,
        user_id: int,
        message: str,
        context: dict = None
    ) -> dict:
        """发送消息并获取AI回复"""

        # 获取或创建会话
        session = await self.get_or_create_chat_session(study_id, user_id)

        # 发送到Dify
        dify_response = await self.dify_service.send_chat_message(
            message=message,
            user_id=str(user_id),
            conversation_id=session.dify_conversation_id,  # 可能为None（首次对话）
            context=context
        )

        # 更新Dify对话ID（如果是首次对话）
        if not session.dify_conversation_id:
            session.dify_conversation_id = dify_response.get("conversation_id")

        # 保存消息记录
        conversation_data = session.conversation_data or {"messages": []}
        conversation_data["messages"].extend([
            {
                "role": "user",
                "content": message,
                "timestamp": datetime.utcnow().isoformat()
            },
            {
                "role": "assistant",
                "content": dify_response.get("answer", ""),
                "timestamp": datetime.utcnow().isoformat()
            }
        ])

        session.conversation_data = conversation_data
        session.updated_at = datetime.utcnow()
        self.db.commit()

        return {
            "response": dify_response.get("answer", ""),
            "local_session_id": session.local_session_id,
            "dify_conversation_id": session.dify_conversation_id,
            "message_id": dify_response.get("message_id")
        }

    async def get_chat_history(
        self,
        study_id: int,
        user_id: int
    ) -> dict:
        """获取对话历史"""

        session = self.db.query(ChatSession).filter(
            ChatSession.study_id == study_id,
            ChatSession.user_id == user_id
        ).first()

        if not session:
            return {
                "exists": False,
                "local_session_id": self.generate_local_session_id(study_id, user_id),
                "messages": []
            }

        return {
            "exists": True,
            "local_session_id": session.local_session_id,
            "dify_conversation_id": session.dify_conversation_id,
            "messages": session.conversation_data.get("messages", []),
            "created_at": session.created_at.isoformat(),
            "updated_at": session.updated_at.isoformat()
        }

class AssessmentService:
    def __init__(self, db: Session):
        self.db = db
        self.dify_service = DifyService()

    async def submit_assessment(
        self,
        study_id: int,
        user_id: int,
        assessment_data: dict
    ) -> dict:
        """提交AI评估（新建或更新）"""

        # 检查是否已存在评估
        existing_assessment = self.db.query(Assessment).filter(
            Assessment.study_id == study_id,
            Assessment.user_id == user_id
        ).first()

        # 调用Dify AI评估
        ai_result = await self.dify_service.submit_assessment(
            assessment_data=assessment_data,
            user_id=str(user_id)
        )

        if existing_assessment:
            # 更新现有评估
            existing_assessment.assessment_data = assessment_data
            existing_assessment.ai_result = ai_result
            existing_assessment.version += 1
            existing_assessment.updated_at = datetime.utcnow()

            self.db.commit()
            self.db.refresh(existing_assessment)

            return {
                "assessment_id": existing_assessment.id,
                "is_update": True,
                "version": existing_assessment.version,
                "ai_result": ai_result
            }
        else:
            # 创建新评估
            new_assessment = Assessment(
                study_id=study_id,
                user_id=user_id,
                assessment_data=assessment_data,
                ai_result=ai_result,
                version=1
            )

            self.db.add(new_assessment)
            self.db.commit()
            self.db.refresh(new_assessment)

            return {
                "assessment_id": new_assessment.id,
                "is_update": False,
                "version": 1,
                "ai_result": ai_result
            }

    async def get_assessment(
        self,
        study_id: int,
        user_id: int
    ) -> dict:
        """获取已有评估（用于编辑）"""

        assessment = self.db.query(Assessment).filter(
            Assessment.study_id == study_id,
            Assessment.user_id == user_id
        ).first()

        if not assessment:
            return {"exists": False}

        return {
            "exists": True,
            "assessment_id": assessment.id,
            "study_id": assessment.study_id,
            "assessment_data": assessment.assessment_data,
            "ai_result": assessment.ai_result,
            "version": assessment.version,
            "updated_at": assessment.updated_at.isoformat()
        }
```

### 7.2 TotalSegmentator集成

#### 7.2.1 分割任务管理
```python
# services/segmentation_service.py
import asyncio
import subprocess
import tempfile
import os
from celery import Celery
from models.segmentation import SegmentationTask
from services.dicom_service import DicomService

celery_app = Celery('segmentation', broker='redis://localhost:6379')

class SegmentationService:
    def __init__(self, db: Session):
        self.db = db
        self.dicom_service = DicomService()

    async def start_segmentation(
        self,
        series_id: int,
        user_id: int,
        algorithm: str = "totalsegmentator"
    ) -> str:
        """启动自动分割任务"""

        # 创建任务记录
        task = SegmentationTask(
            series_id=series_id,
            user_id=user_id,
            algorithm=algorithm,
            status="pending"
        )
        self.db.add(task)
        self.db.commit()
        self.db.refresh(task)

        # 异步执行分割任务
        celery_task = run_segmentation_task.delay(task.id)

        # 更新任务ID
        task.celery_task_id = celery_task.id
        self.db.commit()

        return celery_task.id

    async def get_task_status(self, task_id: str) -> dict:
        """获取任务状态"""

        task = self.db.query(SegmentationTask).filter(
            SegmentationTask.celery_task_id == task_id
        ).first()

        if not task:
            return {"status": "not_found"}

        # 从Celery获取任务状态
        celery_task = celery_app.AsyncResult(task_id)

        return {
            "task_id": task_id,
            "status": celery_task.status.lower(),
            "progress": celery_task.info.get("progress", 0) if celery_task.info else 0,
            "result": celery_task.result if celery_task.successful() else None,
            "error": str(celery_task.info) if celery_task.failed() else None
        }

@celery_app.task(bind=True)
def run_segmentation_task(self, task_id: int):
    """执行TotalSegmentator分割任务"""

    try:
        # 更新任务状态
        self.update_state(state='PROGRESS', meta={'progress': 0})

        # 获取任务信息
        from database import SessionLocal
        db = SessionLocal()
        task = db.query(SegmentationTask).get(task_id)

        if not task:
            raise Exception("Task not found")

        # 获取DICOM数据
        self.update_state(state='PROGRESS', meta={'progress': 10})
        dicom_files = download_dicom_series(task.series_id)

        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            input_dir = os.path.join(temp_dir, "input")
            output_dir = os.path.join(temp_dir, "output")
            os.makedirs(input_dir)
            os.makedirs(output_dir)

            # 复制DICOM文件
            self.update_state(state='PROGRESS', meta={'progress': 20})
            for i, dicom_file in enumerate(dicom_files):
                shutil.copy(dicom_file, input_dir)

            # 运行TotalSegmentator
            self.update_state(state='PROGRESS', meta={'progress': 30})
            cmd = [
                "TotalSegmentator",
                "-i", input_dir,
                "-o", output_dir,
                "--ml",  # 使用机器学习模式
                "--fast"  # 快速模式
            ]

            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # 监控进度
            while process.poll() is None:
                # 这里可以解析TotalSegmentator的输出来获取进度
                self.update_state(state='PROGRESS', meta={'progress': 50})
                time.sleep(5)

            if process.returncode != 0:
                raise Exception(f"TotalSegmentator failed: {process.stderr.read()}")

            # 处理分割结果
            self.update_state(state='PROGRESS', meta={'progress': 80})
            segmentation_result = process_segmentation_output(output_dir)

            # 保存结果到数据库
            self.update_state(state='PROGRESS', meta={'progress': 90})
            save_segmentation_result(task_id, segmentation_result)

            self.update_state(state='PROGRESS', meta={'progress': 100})

        return {"status": "completed", "result": segmentation_result}

    except Exception as e:
        task.status = "failed"
        task.error_message = str(e)
        db.commit()
        raise
    finally:
        db.close()
```

---

## 8. 部署和运维方案

### 8.1 Docker容器化部署

#### 8.1.1 Docker Compose配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:80"
    depends_on:
      - backend
    environment:
      - REACT_APP_API_URL=http://backend:8000
      - REACT_APP_ORTHANC_URL=http://orthanc:8042
    networks:
      - app-network

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    depends_on:
      - mysql
      - redis
      - orthanc
    environment:
      - DATABASE_URL=mysql://user:password@mysql:3306/abdomen_platform
      - REDIS_URL=redis://redis:6379
      - ORTHANC_URL=http://orthanc:8042
      - DIFY_API_URL=${DIFY_API_URL}
      - DIFY_API_KEY=${DIFY_API_KEY}
    volumes:
      - ./data/uploads:/app/uploads
      - ./data/reports:/app/reports
    networks:
      - app-network

  # MySQL数据库
  mysql:
    image: mysql:8.0
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=rootpassword
      - MYSQL_DATABASE=abdomen_platform
      - MYSQL_USER=user
      - MYSQL_PASSWORD=password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - app-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - app-network

  # Orthanc PACS服务
  orthanc:
    image: orthancteam/orthanc:latest
    ports:
      - "8042:8042"  # HTTP端口
      - "4242:4242"  # DICOM端口
    environment:
      - ORTHANC__NAME=AbdomenPlatformPACS
      - ORTHANC__DICOM_AET=ABDOMEN_PACS
      - ORTHANC__DICOM_CHECK_CALLED_AET=false
      - ORTHANC__REMOTE_ACCESS_ALLOWED=true
      - ORTHANC__AUTHENTICATION_ENABLED=false
      - Lua_Script_CallBack_IP=backend
      - Lua_Script_CallBack_PORT=8000
    volumes:
      - orthanc_data:/var/lib/orthanc/db
      - ./orthanc/callback.lua:/etc/orthanc/callback.lua
      - ./orthanc/orthanc.json:/etc/orthanc/orthanc.json
    networks:
      - app-network

  # Celery工作进程
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    command: celery -A app.celery worker --loglevel=info
    depends_on:
      - redis
      - mysql
    environment:
      - DATABASE_URL=mysql://user:password@mysql:3306/abdomen_platform
      - REDIS_URL=redis://redis:6379
    volumes:
      - ./data/segmentation:/app/segmentation
    networks:
      - app-network

  # Celery监控
  celery-flower:
    build:
      context: ./backend
      dockerfile: Dockerfile
    command: celery -A app.celery flower --port=5555
    ports:
      - "5555:5555"
    depends_on:
      - redis
    environment:
      - REDIS_URL=redis://redis:6379
    networks:
      - app-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - app-network

volumes:
  mysql_data:
  redis_data:
  orthanc_data:

networks:
  app-network:
    driver: bridge
```

#### 8.1.2 前端Dockerfile
```dockerfile
# frontend/Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

#### 8.1.3 后端Dockerfile
```dockerfile
# backend/Dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libffi-dev \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 安装TotalSegmentator
RUN pip install TotalSegmentator

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p /app/uploads /app/reports /app/segmentation

EXPOSE 8000

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 8.2 监控和日志方案

#### 8.2.1 Prometheus监控配置
```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'backend'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/metrics'

  - job_name: 'orthanc'
    static_configs:
      - targets: ['orthanc:8042']
    metrics_path: '/statistics'

  - job_name: 'mysql'
    static_configs:
      - targets: ['mysql:3306']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

#### 8.2.2 Grafana仪表板配置
```json
{
  "dashboard": {
    "title": "急性腹痛诊疗平台监控",
    "panels": [
      {
        "title": "API响应时间",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "title": "数据库连接数",
        "type": "singlestat",
        "targets": [
          {
            "expr": "mysql_global_status_threads_connected",
            "legendFormat": "连接数"
          }
        ]
      },
      {
        "title": "DICOM处理量",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(dicom_instances_processed_total[5m])",
            "legendFormat": "实例/秒"
          }
        ]
      },
      {
        "title": "AI任务队列",
        "type": "graph",
        "targets": [
          {
            "expr": "celery_tasks_pending",
            "legendFormat": "待处理任务"
          }
        ]
      }
    ]
  }
}
```

#### 8.2.3 日志收集配置
```yaml
# logging/filebeat.yml
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /var/log/nginx/*.log
  fields:
    service: nginx

- type: log
  enabled: true
  paths:
    - /app/logs/*.log
  fields:
    service: backend

- type: docker
  containers.ids:
    - "*"
  processors:
    - add_docker_metadata: ~

output.elasticsearch:
  hosts: ["elasticsearch:9200"]
  index: "abdomen-platform-%{+yyyy.MM.dd}"

logging.level: info
```

### 8.3 备份和恢复策略

#### 8.3.1 数据库备份脚本
```bash
#!/bin/bash
# scripts/backup_mysql.sh

BACKUP_DIR="/backup/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/abdomen_platform_$DATE.sql"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
docker exec mysql mysqldump \
  -u root -prootpassword \
  --single-transaction \
  --routines \
  --triggers \
  abdomen_platform > $BACKUP_FILE

# 压缩备份文件
gzip $BACKUP_FILE

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete

echo "Database backup completed: $BACKUP_FILE.gz"
```

#### 8.3.2 DICOM数据备份脚本
```bash
#!/bin/bash
# scripts/backup_orthanc.sh

BACKUP_DIR="/backup/orthanc"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/orthanc_data_$DATE.tar.gz"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份Orthanc数据
docker run --rm \
  -v orthanc_data:/data \
  -v $BACKUP_DIR:/backup \
  alpine tar czf /backup/orthanc_data_$DATE.tar.gz -C /data .

# 删除30天前的备份
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

echo "Orthanc backup completed: $BACKUP_FILE"
```

#### 8.3.3 自动备份定时任务
```bash
# crontab配置
# 每天凌晨2点备份数据库
0 2 * * * /opt/scripts/backup_mysql.sh

# 每天凌晨3点备份DICOM数据
0 3 * * * /opt/scripts/backup_orthanc.sh

# 每周日凌晨4点备份应用数据
0 4 * * 0 /opt/scripts/backup_app_data.sh
```

### 8.4 安全配置

#### 8.4.1 Nginx SSL配置
```nginx
# nginx/nginx.conf
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    # 前端静态文件
    location / {
        proxy_pass http://frontend:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # API接口
    location /api/ {
        proxy_pass http://backend:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 增加超时时间（用于AI处理）
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # Orthanc PACS
    location /orthanc/ {
        proxy_pass http://orthanc:8042/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # DICOM文件可能较大
        client_max_body_size 1G;
    }
}
```

---

## 9. 性能优化方案

### 9.1 数据库优化

#### 9.1.1 索引优化策略
```sql
-- 核心查询索引
CREATE INDEX idx_studies_patient_date ON studies(patient_id, study_date);
CREATE INDEX idx_series_study_modality ON series(study_id, modality);
CREATE INDEX idx_instances_series_number ON instances(series_id, instance_number);
CREATE INDEX idx_annotations_series_user ON annotations(series_id, user_id);

-- 复合索引
CREATE INDEX idx_patients_name_id ON patients(patient_name, patient_id);
CREATE INDEX idx_assessments_patient_user_date ON assessments(patient_id, user_id, created_at);

-- 全文搜索索引
ALTER TABLE patients ADD FULLTEXT(patient_name);
ALTER TABLE studies ADD FULLTEXT(study_description);
```

#### 9.1.2 查询优化
```python
# 使用连接查询减少N+1问题
def get_cases_with_details(db: Session, page: int, size: int):
    return db.query(Study)\
        .join(Patient)\
        .join(Series)\
        .options(
            joinedload(Study.patient),
            joinedload(Study.series)
        )\
        .offset((page - 1) * size)\
        .limit(size)\
        .all()

# 使用批量查询
def get_multiple_assessments(db: Session, patient_ids: List[int]):
    return db.query(Assessment)\
        .filter(Assessment.patient_id.in_(patient_ids))\
        .all()
```

### 9.2 缓存优化

#### 9.2.1 Redis缓存策略
```python
# services/cache_service.py
import redis
import json
from typing import Any, Optional

class CacheService:
    def __init__(self):
        self.redis_client = redis.Redis(host='redis', port=6379, db=0)

    def get_dicom_metadata(self, study_uid: str) -> Optional[dict]:
        """获取DICOM元数据缓存"""
        key = f"dicom:study:{study_uid}"
        data = self.redis_client.get(key)
        return json.loads(data) if data else None

    def set_dicom_metadata(self, study_uid: str, metadata: dict, ttl: int = 3600):
        """设置DICOM元数据缓存"""
        key = f"dicom:study:{study_uid}"
        self.redis_client.setex(key, ttl, json.dumps(metadata))

    def get_assessment_result(self, patient_id: int, user_id: int) -> Optional[dict]:
        """获取AI评估结果缓存"""
        key = f"assessment:patient:{patient_id}:user:{user_id}"
        data = self.redis_client.get(key)
        return json.loads(data) if data else None

    def invalidate_patient_cache(self, patient_id: int):
        """清除患者相关缓存"""
        pattern = f"*patient:{patient_id}*"
        keys = self.redis_client.keys(pattern)
        if keys:
            self.redis_client.delete(*keys)
```

### 9.3 前端性能优化

#### 9.3.1 代码分割和懒加载
```typescript
// 路由级别的代码分割
const CaseDetailPage = lazy(() => import('@/pages/CaseDetail'))
const AnnotationPage = lazy(() => import('@/pages/Annotation'))
const SurgicalPlanPage = lazy(() => import('@/pages/SurgicalPlan'))

// 组件级别的懒加载
const DicomViewer = lazy(() => import('@/components/Medical/DicomViewer'))
const VTKViewer = lazy(() => import('@/components/Medical/VTKViewer'))
```

#### 9.3.2 影像数据优化
```typescript
// 影像预加载策略
class ImagePreloader {
  private cache = new Map<string, Promise<any>>()

  preloadSeries(seriesId: string, priority: number = 0) {
    if (this.cache.has(seriesId)) {
      return this.cache.get(seriesId)!
    }

    const promise = this.loadImageData(seriesId)
    this.cache.set(seriesId, promise)

    return promise
  }

  private async loadImageData(seriesId: string) {
    // 分批加载影像数据
    const imageIds = await this.getImageIds(seriesId)
    const batchSize = 10

    for (let i = 0; i < imageIds.length; i += batchSize) {
      const batch = imageIds.slice(i, i + batchSize)
      await Promise.all(batch.map(id => this.loadSingleImage(id)))
    }
  }
}
```

---

## 10. 测试方案

### 10.1 单元测试

#### 10.1.1 后端单元测试
```python
# tests/test_dicom_service.py
import pytest
from services.dicom_service import DicomService
from models.patient import Patient

class TestDicomService:
    def setup_method(self):
        self.dicom_service = DicomService()

    def test_process_dicom_callback(self):
        """测试DICOM回调处理"""
        callback_data = {
            "sop_instance_uid": "*******.5",
            "series_uid": "*******",
            "study_uid": "1.2.3",
            "patient_id": "P001",
            "patient_name": "测试患者",
            "modality": "CT"
        }

        result = self.dicom_service.process_instance_callback(callback_data)
        assert result["status"] == "success"

    def test_invalid_modality_filter(self):
        """测试无效模态过滤"""
        callback_data = {
            "modality": "US"  # 超声，应该被过滤
        }

        result = self.dicom_service.process_instance_callback(callback_data)
        assert result["status"] == "filtered"
```

#### 10.1.2 前端单元测试
```typescript
// tests/components/DicomViewer.test.tsx
import { render, screen } from '@testing-library/react'
import { DicomViewer } from '@/components/Medical/DicomViewer'

describe('DicomViewer', () => {
  test('renders viewer container', () => {
    render(<DicomViewer seriesId="test-series" />)

    const viewer = screen.getByTestId('dicom-viewer')
    expect(viewer).toBeInTheDocument()
  })

  test('loads image data on mount', async () => {
    const mockOnImageLoad = jest.fn()

    render(
      <DicomViewer
        seriesId="test-series"
        onImageLoad={mockOnImageLoad}
      />
    )

    // 等待异步加载完成
    await waitFor(() => {
      expect(mockOnImageLoad).toHaveBeenCalled()
    })
  })
})
```

### 10.2 集成测试

#### 10.2.1 API集成测试
```python
# tests/test_api_integration.py
import pytest
from fastapi.testclient import TestClient
from main import app

client = TestClient(app)

class TestAPIIntegration:
    def test_login_flow(self):
        """测试登录流程"""
        response = client.post("/api/v1/auth/login", json={
            "username": "test_doctor",
            "password": "test_password"
        })

        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data["data"]

        # 使用token访问受保护接口
        token = data["data"]["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        response = client.get("/api/v1/cases", headers=headers)
        assert response.status_code == 200

    def test_dicom_callback_integration(self):
        """测试DICOM回调集成"""
        callback_data = {
            "orthanc_id": "test-id",
            "sop_instance_uid": "*******.5",
            "patient_id": "P001",
            "modality": "CT"
        }

        response = client.post("/api/v1/images/callback/", json=callback_data)
        assert response.status_code == 200

        # 验证数据是否正确存储
        response = client.get("/api/v1/cases")
        cases = response.json()["data"]["items"]
        assert len(cases) > 0
```

### 10.3 性能测试

#### 10.3.1 负载测试脚本
```python
# tests/load_test.py
import asyncio
import aiohttp
import time
from concurrent.futures import ThreadPoolExecutor

async def test_api_performance():
    """API性能测试"""
    async with aiohttp.ClientSession() as session:
        # 并发请求测试
        tasks = []
        for i in range(100):
            task = session.get('http://localhost:8000/api/v1/cases')
            tasks.append(task)

        start_time = time.time()
        responses = await asyncio.gather(*tasks)
        end_time = time.time()

        success_count = sum(1 for r in responses if r.status == 200)
        total_time = end_time - start_time

        print(f"成功请求: {success_count}/100")
        print(f"总耗时: {total_time:.2f}秒")
        print(f"平均响应时间: {total_time/100:.3f}秒")
        print(f"QPS: {100/total_time:.2f}")

if __name__ == "__main__":
    asyncio.run(test_api_performance())
```

---

## 11. 总结

本技术方案说明书详细描述了急性腹痛诊疗平台的完整技术实现方案，涵盖了：

### 11.1 核心技术特点
- **现代化技术栈**：React + TypeScript前端，Python + FastAPI后端
- **标准化DICOM集成**：基于Orthanc PACS的完整DICOM处理流程
- **AI智能化**：集成Dify对话平台和TotalSegmentator自动分割
- **高性能影像处理**：Cornerstone3D.js + VTK.js实现专业影像查看
- **容器化部署**：Docker + Docker Compose实现一键部署

### 11.2 系统优势
- **高可扩展性**：微服务架构支持水平扩展
- **高可用性**：完善的监控、备份和恢复机制
- **高性能**：多级缓存和数据库优化策略
- **高安全性**：JWT认证、HTTPS加密、权限控制
- **易维护性**：完整的测试覆盖和文档体系

### 11.3 实施建议
1. **分阶段实施**：按照PRD中的里程碑分阶段开发和部署
2. **持续集成**：建立CI/CD流水线，确保代码质量
3. **性能监控**：部署完整的监控体系，及时发现和解决问题
4. **用户培训**：提供充分的用户培训和技术支持
5. **迭代优化**：根据用户反馈持续优化系统功能和性能

本技术方案为急性腹痛诊疗平台的成功实施提供了坚实的技术基础，确保系统能够满足医疗行业的高标准要求。

---

**文档结束**
```
```
```
