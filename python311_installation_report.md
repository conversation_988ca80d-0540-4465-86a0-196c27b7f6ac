# Python 3.11 安装报告

## 安装概述
- **安装日期**: 2025-07-10
- **系统环境**: Ubuntu 24.04.2 LTS (Noble Numbat)
- **安装方式**: 通过官方APT包管理器
- **Python版本**: 3.11.13

## 安装过程

### 1. 系统环境检查
- 原有Python版本: Python 3.12.3 (系统默认)
- 操作系统: Ubuntu 24.04.2 LTS
- 包管理器: APT

### 2. 安装的组件
通过以下命令安装了完整的Python 3.11环境：
```bash
sudo apt update
sudo apt install -y python3.11 python3.11-dev python3.11-venv python3.11-distutils
```

### 3. 安装的包列表
- `python3.11` - Python 3.11解释器
- `python3.11-dev` - 开发头文件和静态库
- `python3.11-venv` - 虚拟环境支持
- `python3.11-distutils` - 包分发工具
- `libpython3.11` - 共享运行时库
- `libpython3.11-dev` - 开发库
- `libpython3.11-minimal` - 最小运行时库
- `libpython3.11-stdlib` - 标准库
- `python3.11-lib2to3` - 代码转换工具
- `python3.11-minimal` - 最小Python环境

### 4. pip安装
通过官方bootstrap脚本安装pip：
```bash
curl -sS https://bootstrap.pypa.io/get-pip.py | python3.11
```

## 验证结果

### Python版本验证
```bash
$ python3.11 --version
Python 3.11.13
```

### pip版本验证
```bash
$ python3.11 -m pip --version
pip 25.1.1 from /home/<USER>/.local/lib/python3.11/site-packages/pip (python 3.11)
```

### 虚拟环境测试
成功创建和激活Python 3.11虚拟环境：
```bash
$ python3.11 -m venv test_env
$ source test_env/bin/activate
(test_env) $ python --version
Python 3.11.13
```

## 系统中的Python版本

当前系统中可用的Python版本：
- Python 3.8 (`/usr/bin/python3.8`)
- Python 3.10 (`/usr/bin/python3.10`)
- **Python 3.11** (`/usr/bin/python3.11`) - 新安装
- Python 3.12 (`/usr/bin/python3.12`) - 系统默认

## 使用方法

### 直接使用Python 3.11
```bash
python3.11 your_script.py
```

### 创建Python 3.11虚拟环境
```bash
# 创建虚拟环境
python3.11 -m venv myproject_env

# 激活虚拟环境
source myproject_env/bin/activate

# 在虚拟环境中安装包
pip install package_name

# 退出虚拟环境
deactivate
```

### 使用pip安装包
```bash
# 为Python 3.11安装包
python3.11 -m pip install package_name

# 或在激活的虚拟环境中
pip install package_name
```

## 注意事项

1. **系统默认Python**: 系统默认的`python3`和`python`仍然指向Python 3.12
2. **包管理**: 建议使用虚拟环境来管理不同项目的依赖
3. **兼容性**: Python 3.11与大多数现代Python包兼容
4. **性能**: Python 3.11相比3.10有显著的性能提升

## 推荐用法

### 为急腹症平台项目创建专用环境
```bash
cd /home/<USER>/ResearchProjects/acute_abdomen_platform/aap_api
python3.11 -m venv venv_py311
source venv_py311/bin/activate
pip install -r requirements.txt
```

### 设置别名（可选）
在`~/.bashrc`中添加：
```bash
alias python311='python3.11'
alias pip311='python3.11 -m pip'
```

## 故障排除

### 如果遇到权限问题
```bash
# 使用用户级安装
python3.11 -m pip install --user package_name
```

### 如果需要更新pip
```bash
python3.11 -m pip install --upgrade pip
```

### 如果需要卸载Python 3.11
```bash
sudo apt remove python3.11 python3.11-dev python3.11-venv python3.11-distutils
sudo apt autoremove
```

## 总结

✅ Python 3.11.13 安装成功  
✅ pip 25.1.1 安装成功  
✅ 虚拟环境功能正常  
✅ 开发工具完整  

Python 3.11现在可以在您的系统中正常使用，可以通过`python3.11`命令调用。建议在项目中使用虚拟环境来管理依赖，确保项目的独立性和可重现性。
